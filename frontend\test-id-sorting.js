// 测试基于ID的排序逻辑
const testMessages = [
  { id: 1, role: 'user', content: '第一条用户消息', created_at: '2024-01-01T10:00:00Z' },
  { id: 2, role: 'assistant', content: '第一条助手回复', created_at: '2024-01-01T10:05:00Z' },
  { id: 3, role: 'tool_call', tool_name: 'search', content: '工具调用: search', created_at: '2024-01-01T10:02:00Z' },
  { id: 4, role: 'user', content: '第二条用户消息', created_at: '2024-01-01T10:01:00Z' },
  { id: 5, role: 'assistant', content: '第二条助手回复', created_at: '2024-01-01T10:06:00Z' },
];

console.log('原始消息顺序（模拟数据库存储，时间戳可能乱序）:');
testMessages.forEach(msg => {
  console.log(`ID:${msg.id} Role:${msg.role} Time:${msg.created_at} Content:${msg.content}`);
});

// 模拟数据库排序：只按ID排序
const dbSorted = [...testMessages].sort((a, b) => a.id - b.id);

console.log('\n数据库排序结果（ORDER BY id ASC）:');
dbSorted.forEach(msg => {
  console.log(`ID:${msg.id} Role:${msg.role} Content:${msg.content}`);
});

// 模拟前端排序逻辑（也是按ID排序）
const frontendSorted = [...testMessages].sort((a, b) => a.id - b.id);

console.log('\n前端排序结果:');
frontendSorted.forEach(msg => {
  console.log(`ID:${msg.id} Role:${msg.role} Content:${msg.content}`);
});

// 验证排序一致性
const isConsistent = JSON.stringify(dbSorted) === JSON.stringify(frontendSorted);
console.log(`\n排序一致性检查: ${isConsistent ? '✅ 一致' : '❌ 不一致'}`);

// 验证ID顺序是否正确（应该是递增的）
let idOrderCorrect = true;
for (let i = 1; i < dbSorted.length; i++) {
  if (dbSorted[i].id <= dbSorted[i - 1].id) {
    idOrderCorrect = false;
    console.log(`❌ ID顺序错误: ${dbSorted[i - 1].id} 应该小于 ${dbSorted[i].id}`);
    break;
  }
}

if (idOrderCorrect) {
  console.log('✅ ID顺序正确（严格递增）');
}

// 验证消息类型混合排序
const messageTypes = dbSorted.map(msg => msg.role);
console.log(`\n消息类型顺序: ${messageTypes.join(' → ')}`);

if (isConsistent && idOrderCorrect) {
  console.log('\n🎉 基于ID的排序修复成功！');
  console.log('优势：');
  console.log('- 排序逻辑极其简单，只依赖自增ID');
  console.log('- 不受时间戳、序列号等复杂逻辑影响');
  console.log('- 数据库和前端排序完全一致');
  console.log('- 工具调用消息与普通消息统一排序');
} else {
  console.log('\n❌ 排序仍有问题，需要进一步调试。');
}
