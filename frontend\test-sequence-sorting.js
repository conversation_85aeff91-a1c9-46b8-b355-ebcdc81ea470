// 简单的排序逻辑测试脚本
const testMessages = [
  { id: 1, sequence_number: 1, timestamp: 1000, role: 'user', content: '第一条用户消息' },
  { id: 2, sequence_number: 2, timestamp: 5000, role: 'assistant', content: '第一条助手回复' }, // 时间戳较晚但序列号正确
  { id: 3, sequence_number: 3, timestamp: 2000, role: 'user', content: '第二条用户消息' }, // 时间戳较早但序列号正确
  { id: 4, sequence_number: 4, timestamp: 6000, role: 'assistant', content: '第二条助手回复' },
];

console.log('原始消息顺序（模拟数据库存储顺序）:');
testMessages.forEach(msg => {
  console.log(`ID:${msg.id} Seq:${msg.sequence_number} Time:${msg.timestamp} Role:${msg.role}`);
});

// 模拟数据库排序：sequence_number ASC, timestamp ASC
const dbSorted = [...testMessages].sort((a, b) => {
  if (a.sequence_number !== b.sequence_number) {
    return a.sequence_number - b.sequence_number;
  }
  return a.timestamp - b.timestamp;
});

console.log('\n数据库排序结果（sequence_number ASC, timestamp ASC）:');
dbSorted.forEach(msg => {
  console.log(`ID:${msg.id} Seq:${msg.sequence_number} Time:${msg.timestamp} Role:${msg.role} Content:${msg.content}`);
});

// 模拟前端排序逻辑
const frontendSorted = [...testMessages].sort((a, b) => {
  if (a.sequence_number !== b.sequence_number) {
    return a.sequence_number - b.sequence_number;
  }
  return a.timestamp - b.timestamp;
});

console.log('\n前端排序结果:');
frontendSorted.forEach(msg => {
  console.log(`ID:${msg.id} Seq:${msg.sequence_number} Time:${msg.timestamp} Role:${msg.role} Content:${msg.content}`);
});

// 验证排序一致性
const isConsistent = JSON.stringify(dbSorted) === JSON.stringify(frontendSorted);
console.log(`\n排序一致性检查: ${isConsistent ? '✅ 一致' : '❌ 不一致'}`);

// 验证对话逻辑顺序
const expectedOrder = ['第一条用户消息', '第一条助手回复', '第二条用户消息', '第二条助手回复'];
const actualOrder = dbSorted.map(msg => msg.content);
const logicCorrect = JSON.stringify(expectedOrder) === JSON.stringify(actualOrder);
console.log(`对话逻辑顺序检查: ${logicCorrect ? '✅ 正确' : '❌ 错误'}`);

if (logicCorrect) {
  console.log('\n🎉 排序修复成功！消息按正确的对话逻辑顺序排列。');
} else {
  console.log('\n❌ 排序仍有问题，需要进一步调试。');
  console.log('期望顺序:', expectedOrder);
  console.log('实际顺序:', actualOrder);
}
