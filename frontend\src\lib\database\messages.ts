import { db } from './connection';
import { conversationOperations } from './conversations';
import type { Message, CreateMessageData } from './types';

// 消息相关查询语句
export const messageQueries = {
  // 创建新消息
  create: db.prepare(`
    INSERT INTO messages (
      conversation_id, role, content, model, sequence_number, timestamp,
      total_duration, load_duration, prompt_eval_count, prompt_eval_duration,
      eval_count, eval_duration
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `),

  // 获取对话的所有消息
  getByConversationId: db.prepare(`
    SELECT * FROM messages
    WHERE conversation_id = ?
    ORDER BY sequence_number ASC, created_at ASC
  `),

  // 删除对话的所有消息
  deleteByConversationId: db.prepare(`
    DELETE FROM messages
    WHERE conversation_id = ?
  `),

  // 获取对话中最后使用的模型
  getLastModelByConversationId: db.prepare(`
    SELECT model FROM messages
    WHERE conversation_id = ? AND model IS NOT NULL
    ORDER BY created_at DESC
    LIMIT 1
  `),

  // 获取对话中下一个可用的序列号
  getNextSequenceNumber: db.prepare(`
    SELECT COALESCE(MAX(sequence_number), 0) + 1 as next_sequence
    FROM messages
    WHERE conversation_id = ?
  `),
};

// 消息数据库操作函数
export const messageOperations = {
  // 创建新消息
  create(data: CreateMessageData): number {
    // 自动获取下一个序列号
    const sequenceNumber = data.sequence_number || messageOperations.getNextSequenceNumber(data.conversation_id);
    // 生成时间戳（毫秒级）
    const timestamp = Date.now();

    const result = messageQueries.create.run(
      data.conversation_id,
      data.role,
      data.content,
      data.model || null,
      sequenceNumber,
      timestamp,
      data.total_duration || null,
      data.load_duration || null,
      data.prompt_eval_count || null,
      data.prompt_eval_duration || null,
      data.eval_count || null,
      data.eval_duration || null
    );
    // 更新对话的时间戳
    conversationOperations.updateTimestamp(data.conversation_id);
    return result.lastInsertRowid as number;
  },

  // 获取对话的所有消息
  getByConversationId(conversationId: number): Message[] {
    return messageQueries.getByConversationId.all(conversationId) as Message[];
  },

  // 删除对话的所有消息
  deleteByConversationId(conversationId: number): void {
    messageQueries.deleteByConversationId.run(conversationId);
  },

  // 获取对话中最后使用的模型
  getLastModelByConversationId(conversationId: number): string | null {
    const result = messageQueries.getLastModelByConversationId.get(conversationId) as { model: string } | undefined;
    return result?.model || null;
  },

  // 获取对话中下一个可用的序列号
  getNextSequenceNumber(conversationId: number): number {
    const result = messageQueries.getNextSequenceNumber.get(conversationId) as { next_sequence: number } | undefined;
    return result?.next_sequence || 1;
  },
};