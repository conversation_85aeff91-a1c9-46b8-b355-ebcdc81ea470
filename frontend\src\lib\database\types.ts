// 对话相关接口
export interface Conversation {
  id: number;
  title: string;
  model: string;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: number;
  conversation_id: number;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  model?: string;
  sequence_number?: number;
  created_at: string;
  timestamp?: number; // 用于消息排序的时间戳
  // Ollama生成统计信息
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

export interface CreateConversationData {
  title: string;
  model: string;
}

export interface CreateMessageData {
  conversation_id: number;
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  model?: string;
  sequence_number?: number;
  // Ollama生成统计信息
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

// MCP相关接口
export interface McpServer {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  type: 'stdio' | 'sse' | 'streamable-http';
  status: 'connected' | 'disconnected' | 'error' | 'connecting';
  enabled: boolean;
  
  // STDIO配置
  command?: string;
  args?: string; // JSON数组格式
  working_directory?: string;
  
  // SSE/HTTP配置
  url?: string;
  base_url?: string;
  port?: number;
  path?: string;
  protocol?: 'http' | 'https';
  
  // 通用配置
  headers?: string; // JSON对象格式
  auth_type?: 'none' | 'bearer' | 'basic' | 'api_key';
  auth_config?: string; // JSON格式
  timeout_ms?: number;
  retry_attempts?: number;
  retry_delay_ms?: number;
  
  // 扩展配置
  extra_config?: string; // JSON格式
  
  created_at: string;
  updated_at: string;
  last_connected_at?: string;
  error_message?: string;
}

export interface McpTool {
  id: number;
  server_id: number;
  name: string;
  description?: string;
  input_schema?: string; // JSON格式
  is_available: boolean;
  enabled: boolean;
  last_used_at?: string;
  usage_count: number;
  created_at: string;
  updated_at: string;
}

export interface McpToolCall {
  id: number;
  tool_id: number;
  conversation_id: number;
  message_id?: number;
  input_args?: string; // JSON格式
  output_result?: string; // JSON格式
  execution_time_ms?: number;
  status: 'success' | 'error' | 'timeout';
  error_message?: string;
  created_at: string;
}

export interface CreateMcpServerData {
  name: string;
  display_name: string;
  description?: string;
  type: 'stdio' | 'sse' | 'streamable-http';
  enabled?: boolean;
  
  // STDIO配置
  command?: string;
  args?: string[];
  working_directory?: string;
  
  // SSE/HTTP配置
  url?: string;
  base_url?: string;
  port?: number;
  path?: string;
  protocol?: 'http' | 'https';
  
  // 通用配置
  headers?: Record<string, string>;
  auth_type?: 'none' | 'bearer' | 'basic' | 'api_key';
  auth_config?: Record<string, any>;
  timeout_ms?: number;
  retry_attempts?: number;
  retry_delay_ms?: number;
  
  // 扩展配置
  extra_config?: Record<string, any>;
}

export interface CreateMcpToolData {
  server_id: number;
  name: string;
  description?: string;
  input_schema?: Record<string, any>;
  is_available?: boolean;
  enabled?: boolean;
}

export interface CreateMcpToolCallData {
  tool_id: number;
  conversation_id: number;
  message_id?: number;
  input_args?: Record<string, any>;
  output_result?: Record<string, any>;
  execution_time_ms?: number;
  status: 'success' | 'error' | 'timeout';
  error_message?: string;
}