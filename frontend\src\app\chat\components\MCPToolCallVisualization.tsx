'use client';

import React, { useState } from 'react';
import { ChevronDown, ChevronUp, <PERSON><PERSON><PERSON><PERSON>, Settings, XCircle, Clock } from 'lucide-react';

interface MCPToolCallVisualizationProps {
  content: string;
  isLoading?: boolean;
  toolName?: string;
  isRealTime?: boolean;
  activeToolCalls?: Map<string, any>;
}

interface ParsedToolCall {
  toolName: string;
  parameters: string;
  result: string;
}

export function MCPToolCallVisualization({ content, isLoading, toolName, isRealTime, activeToolCalls }: MCPToolCallVisualizationProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(true);
  
  // 从activeToolCalls状态获取工具调用信息
  const getToolCallFromState = (): ParsedToolCall | null => {
    if (!activeToolCalls || activeToolCalls.size === 0) {
      return null;
    }
    
    // 获取最新的工具调用（通常是最后一个）
    const toolCallsArray = Array.from(activeToolCalls.values());
    const latestToolCall = toolCallsArray[toolCallsArray.length - 1];
    
    if (!latestToolCall) {
      return null;
    }
    
    let result = '';
    if (latestToolCall.status === 'executing') {
      result = '执行中...';
    } else if (latestToolCall.status === 'completed') {
      result = latestToolCall.result || '执行完成';
    } else if (latestToolCall.status === 'error') {
      result = `错误: ${latestToolCall.error || '未知错误'}`;
    }
    
    return {
      toolName: latestToolCall.toolName,
      parameters: JSON.stringify(latestToolCall.args || {}, null, 2),
      result: result
    };
  };

  const parsedContent = getToolCallFromState();
  
  // 检查是否是执行中状态
  const isExecuting = parsedContent?.result === '执行中...' || (isLoading && !parsedContent);
  const hasError = parsedContent?.result?.includes('错误:') || false;
  
  // 如果没有工具调用信息且不在加载状态，返回null
  if (!parsedContent && !isLoading && (!activeToolCalls || activeToolCalls.size === 0)) {
    return null;
  }

  // Loading动画组件
  const LoadingSpinner = () => (
    <div className="flex items-center justify-center">
      <div className="relative">
        <div className="w-8 h-8 border-4 border-blue-200 dark:border-blue-800 rounded-full animate-spin">
          <div className="absolute top-0 left-0 w-8 h-8 border-4 border-transparent border-t-blue-600 dark:border-t-blue-400 rounded-full animate-spin"></div>
        </div>
      </div>
    </div>
  );

  // 检查结果内容是否超过6行
  const checkContentHeight = (text: string) => {
    const lines = text.split('\n');
    return lines.length > 6;
  };

  const resultContent = parsedContent?.result || '';
  const isContentLong = checkContentHeight(resultContent);
  const shouldShowExpandButton = isContentLong;

  // 渲染结果内容
  const renderResultContent = () => {
    if (!parsedContent) return null;
    
    // 处理结果内容，支持URL、Title等格式
    const formatContent = (content: string) => {
      // 检测并格式化URL
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      // 检测Title格式
       const titleRegex = /Title:\s*(.+?)(?=\n|$)/g;
       // 检测Content格式
       const contentRegex = /Content:\s*([\s\S]*?)(?=\n\n|$)/g;
       // 检测图片URL
       const imageRegex = /(https?:\/\/[^\s]+\.(jpg|jpeg|png|gif|webp|svg))/gi;
      
      let formattedContent = content;
      
      // 先处理图片URL
       const imageMatches = new Set();
       formattedContent = formattedContent.replace(imageRegex, (match) => {
         imageMatches.add(match);
         return `<img src="${match}" alt="图片" style="max-height: 88px; object-fit: contain; border-radius: 4px; margin: 4px 0;" />`;
       });
       
       // 处理非图片URL链接
       formattedContent = formattedContent.replace(urlRegex, (match) => {
         if (!imageMatches.has(match)) {
           return `<a href="${match}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:underline break-all">${match}</a>`;
         }
         return match;
       });
      
      // 处理Title格式
       formattedContent = formattedContent.replace(titleRegex, (match, title) => {
         return `<div class="font-semibold text-gray-900 dark:text-gray-100 mb-1">📄 ${title.trim()}</div>`;
       });
       
       // 处理Content格式
       formattedContent = formattedContent.replace(contentRegex, (match, content) => {
         return `<div class="text-gray-700 dark:text-gray-300 mt-2 pl-4 border-l-2 border-gray-300 dark:border-gray-600">${content.trim()}</div>`;
       });
       
       // 处理换行，保持基本格式
       formattedContent = formattedContent.replace(/\n/g, '<br>');
       
       return formattedContent;
    };
    
    return (
      <div 
        className={`text-sm overflow-hidden transition-all duration-300 ${
          !isExpanded && isContentLong ? 'max-h-24' : ''
        }`}
        style={{ 
          maxHeight: isExpanded ? 'none' : isContentLong ? '6rem' : 'auto',
          overflowY: isExpanded ? 'auto' : 'hidden'
        }}
        dangerouslySetInnerHTML={{ __html: formatContent(resultContent) }}
      />
    );
  };

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20">
      {/* 步骤1: 工具执行阶段 */}
      <div className="p-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-xs font-bold">
              1
            </div>
            <Settings className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <span className="font-medium text-sm text-gray-900 dark:text-gray-100">
              {(isLoading || isExecuting) ? '工具执行' : '工具执行'}
            </span>
          </div>
          
          <div className="flex items-center gap-2 ml-auto">
            {(isLoading || isExecuting) ? (
              <>
                <div className="w-4 h-4 border-2 border-blue-200 dark:border-blue-800 border-t-blue-600 dark:border-t-blue-400 rounded-full animate-spin"></div>
                <span className="text-xs text-blue-600 dark:text-blue-400">执行中...</span>
              </>
            ) : (
              <>
                <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                <span className="text-xs text-green-600 dark:text-green-400">已完成</span>
              </>
            )}
            
            {/* 折叠/展开按钮 */}
            <button
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="ml-2 p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
            >
              {isCollapsed ? (
                <ChevronDown className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              ) : (
                <ChevronUp className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              )}
            </button>
          </div>
        </div>
        
        {!isCollapsed && (
          <div className="mt-2 ml-8">
            <div className="text-sm">
              <span className="font-medium text-gray-700 dark:text-gray-300">工具名称: </span>
              <span className="text-blue-600 dark:text-blue-400 font-mono">
                {(isLoading || isExecuting) ? (toolName || parsedContent?.toolName || '加载中...') : parsedContent?.toolName}
              </span>
            </div>
            
            {!isLoading && parsedContent && (
              <div className="mt-2">
                <span className="font-medium text-gray-700 dark:text-gray-300">输入参数:</span>
                <pre className="mt-1 text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded border overflow-x-auto">
                  {parsedContent.parameters}
                </pre>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 步骤2: 工具反馈阶段 */}
      {!isCollapsed && parsedContent && (
        <div className="p-3">
          <div className="flex items-center gap-3 mb-3">
            <div className="flex items-center gap-2">
              <div className={`w-6 h-6 text-white rounded-full flex items-center justify-center text-xs font-bold ${
                hasError ? 'bg-red-600' : (isExecuting || isLoading) ? 'bg-gray-400' : 'bg-green-600'
              }`}>
                2
              </div>
              {hasError ? (
                <XCircle className="w-4 h-4 text-red-600 dark:text-red-400" />
              ) : (isExecuting || isLoading) ? (
                <Clock className="w-4 h-4 text-gray-600 dark:text-gray-400" />
              ) : (
                <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
              )}
              <span className="font-medium text-sm text-gray-900 dark:text-gray-100">
                 {hasError ? '工具反馈（执行失败）' : (isExecuting || isLoading) ? '工具反馈（等待中）' : '工具反馈'}
               </span>
            </div>
            
            <div className="flex items-center gap-1 ml-auto">
              <Clock className="w-3 h-3 text-gray-500" />
              <span className="text-xs text-gray-500">执行完成</span>
            </div>
          </div>
          
          <div className="ml-8">
            <div className="font-medium text-gray-700 dark:text-gray-300 mb-2">输出结果:</div>
            <div className={`p-3 rounded border relative ${
              hasError ? 'bg-red-50 dark:bg-red-900/20' : 
              (isExecuting || isLoading) ? 'bg-yellow-50 dark:bg-yellow-900/20' : 
              'bg-gray-50 dark:bg-gray-800'
            }`}>
              {(isExecuting || isLoading) ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-yellow-200 dark:border-yellow-800 border-t-yellow-600 dark:border-t-yellow-400 rounded-full animate-spin"></div>
                  <span className="text-yellow-700 dark:text-yellow-300">正在执行工具调用...</span>
                </div>
              ) : (
                <div className={`${
                  hasError ? 'text-red-700 dark:text-red-300' :
                  'text-gray-700 dark:text-gray-300'
                }`}>
                  {renderResultContent()}
                </div>
              )}
              
              {/* 渐变遮罩效果，当内容被截断时显示 */}
              {!isExpanded && isContentLong && (
                <div className={`absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t pointer-events-none ${
                  hasError ? 'from-red-50 dark:from-red-900/20' :
                  (isExecuting || isLoading) ? 'from-yellow-50 dark:from-yellow-900/20' :
                  'from-gray-50 dark:from-gray-800'
                } to-transparent`}></div>
              )}
            </div>
            
            {/* 展开/收起按钮 */}
            {shouldShowExpandButton && (
              <button
                onClick={() => setIsExpanded(!isExpanded)}
                className="mt-2 flex items-center gap-1 text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="w-3 h-3" />
                    收起内容
                  </>
                ) : (
                  <>
                    <ChevronDown className="w-3 h-3" />
                    展开完整内容
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}