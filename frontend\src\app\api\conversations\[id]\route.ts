import { NextRequest, NextResponse } from 'next/server';
import { dbOperations } from '../../../../lib/database';

// 获取单个对话及其消息
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const conversationId = parseInt(id);
    
    if (isNaN(conversationId)) {
      return NextResponse.json(
        { error: '无效的对话ID' },
        { status: 400 }
      );
    }

    // 获取对话信息
    const conversation = dbOperations.getConversationById(conversationId);
    if (!conversation) {
      return NextResponse.json(
        { error: '对话不存在' },
        { status: 404 }
      );
    }

    // 获取对话的所有消息
    const messages = dbOperations.getMessagesByConversationId(conversationId);
    
    // 获取对话的工具调用记录
    const toolCallRecords = await dbOperations.getMcpToolCallsByConversationId(conversationId);
    
    // 获取对话中最后使用的模型
    const lastModel = await dbOperations.getLastModelByConversationId(conversationId);
    
    return NextResponse.json({
      success: true,
      conversation,
      messages,
      toolCallRecords,
      lastModel
    });
  } catch (error) {
    console.error('获取对话失败:', error);
    
    return NextResponse.json(
      { 
        error: '获取对话失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 更新对话标题
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const conversationId = parseInt(id);
    
    if (isNaN(conversationId)) {
      return NextResponse.json(
        { error: '无效的对话ID' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { title }: { title: string } = body;

    if (!title || !title.trim()) {
      return NextResponse.json(
        { error: '标题不能为空' },
        { status: 400 }
      );
    }

    // 检查对话是否存在
    const conversation = dbOperations.getConversationById(conversationId);
    if (!conversation) {
      return NextResponse.json(
        { error: '对话不存在' },
        { status: 404 }
      );
    }

    // 更新对话标题
    dbOperations.updateConversationTitle(conversationId, title.trim());
    
    // 获取更新后的对话
    const updatedConversation = dbOperations.getConversationById(conversationId);
    
    return NextResponse.json({
      success: true,
      conversation: updatedConversation
    });
  } catch (error) {
    console.error('更新对话失败:', error);
    
    return NextResponse.json(
      { 
        error: '更新对话失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 删除对话
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const conversationId = parseInt(id);
    
    if (isNaN(conversationId)) {
      return NextResponse.json(
        { error: '无效的对话ID' },
        { status: 400 }
      );
    }

    // 检查对话是否存在
    const conversation = dbOperations.getConversationById(conversationId);
    if (!conversation) {
      return NextResponse.json(
        { error: '对话不存在' },
        { status: 404 }
      );
    }

    // 删除对话（会级联删除相关消息）
    dbOperations.deleteConversation(conversationId);
    
    return NextResponse.json({
      success: true,
      message: '对话已删除'
    });
  } catch (error) {
    console.error('删除对话失败:', error);
    
    return NextResponse.json(
      { 
        error: '删除对话失败',
        message: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}