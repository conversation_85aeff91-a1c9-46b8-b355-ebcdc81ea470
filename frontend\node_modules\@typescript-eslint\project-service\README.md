# `@typescript-eslint/project-service`

> Standalone TypeScript project service wrapper for linting.

[![NPM Version](https://img.shields.io/npm/v/@typescript-eslint/project-service.svg?style=flat-square)](https://www.npmjs.com/package/@typescript-eslint/project-service)
[![NPM Downloads](https://img.shields.io/npm/dm/@typescript-eslint/project-service.svg?style=flat-square)](https://www.npmjs.com/package/@typescript-eslint/project-service)

A standalone export of the "Project Service" that powers typed linting for typescript-eslint.

> See https://typescript-eslint.io for general documentation on typescript-eslint, the tooling that allows you to run ESLint and Prettier on TypeScript code.

<!-- Local path for docs: docs/packages/Project_Service.mdx -->
