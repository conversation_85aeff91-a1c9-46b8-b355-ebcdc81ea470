{"name": "@typescript-eslint/scope-manager", "version": "8.34.0", "description": "TypeScript scope analyser for ESLint", "files": ["dist", "!*.tsbuil<PERSON><PERSON>", "package.json", "README.md", "LICENSE"], "type": "commonjs", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/scope-manager"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "homepage": "https://typescript-eslint.io/packages/scope-manager", "license": "MIT", "keywords": ["eslint", "typescript", "estree"], "scripts": {"//": "These package scripts are mostly here for convenience. Task running is handled by Nx at the root level.", "build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "clean-fixtures": "rimraf -g \"./src/**/fixtures/**/snapshots\"", "format": "yarn run -T format", "generate-lib": "yarn run -BT nx generate-lib repo", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "dependencies": {"@typescript-eslint/types": "8.34.0", "@typescript-eslint/visitor-keys": "8.34.0"}, "devDependencies": {"@typescript-eslint/typescript-estree": "8.34.0", "@vitest/coverage-v8": "^3.1.3", "@vitest/pretty-format": "^3.1.3", "glob": "*", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "nx": {"name": "scope-manager", "includedScripts": ["clean", "clean-fixtures"]}}