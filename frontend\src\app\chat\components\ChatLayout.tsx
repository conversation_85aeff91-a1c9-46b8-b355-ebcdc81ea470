'use client';

import React from 'react';
import { Sidebar } from '../../Sidebar';
import { ChatArea } from '@/app/chat/components/ChatArea';
import { WelcomePage } from './WelcomePage';
import { Conversation, Message } from '@/lib/database';
import { OllamaModel, AIState } from '../types';

interface ToolCallMessageType {
  id: string;
  type: 'tool_call';
  toolName: string;
  args: any;
  status: 'executing' | 'completed' | 'error';
  result?: string;
  error?: string;
  startTime: number;
  executionTime?: number;
}

interface ChatLayoutProps {
  models: OllamaModel[];
  conversations: Conversation[];
  currentConversation: Conversation | null;
  messages: Message[];
  selectedModel: string;
  inputMessage: string;
  isStreaming: boolean;
  error: string | null;
  showModelSelector: boolean;
  enableTools: boolean;
  selectedTools: string[];
  aiState?: AIState;
  activeToolCalls?: Map<string, any>;
  toolCallMessages: ToolCallMessageType[];
  setSelectedModel: (model: string) => void;
  setInputMessage: (message: string) => void;
  setError: (error: string | null) => void;
  setShowModelSelector: (show: boolean) => void;
  setEnableTools: (enable: boolean) => void;
  setSelectedTools: (tools: string[]) => void;
  loadConversation: (id: number) => void;
  createNewConversation: () => void;
  deleteConversation: (id: number) => void;
  sendMessage: () => void;
  stopGeneration: () => void;
  onToolsToggle: (enabled: boolean) => void;
  clearCurrentChat?: () => void;
}



export function ChatLayout(props: ChatLayoutProps) {
  const {
    models,
    conversations,
    currentConversation,
    messages,
    selectedModel,
    inputMessage,
    isStreaming,
    error,
    showModelSelector,
    enableTools,
    selectedTools,
    aiState,
    activeToolCalls,
    toolCallMessages,
    setSelectedModel,
    setInputMessage,
    setShowModelSelector,
    loadConversation,
    createNewConversation,
    deleteConversation,
    sendMessage,
    stopGeneration,
    onToolsToggle,
    setSelectedTools,
    clearCurrentChat,
  } = props;
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900">
      <Sidebar
        models={models}
        conversations={conversations}
        currentConversation={currentConversation}
        selectedModel={selectedModel}
        showModelSelector={showModelSelector}
        onModelChange={setSelectedModel}
        onShowModelSelector={setShowModelSelector}
        onCreateConversation={createNewConversation}
        onLoadConversation={loadConversation}
        onDeleteConversation={deleteConversation}
      />
      
      <div className="flex-1 flex flex-col">
        {currentConversation ? (
          <ChatArea
            currentConversation={currentConversation}
            messages={messages}
            inputMessage={inputMessage}
            isStreaming={isStreaming}
            error={error}
            selectedModel={selectedModel}
            enableTools={enableTools}
            selectedTools={selectedTools}
            aiState={aiState}
            activeToolCalls={activeToolCalls}
            toolCallMessages={toolCallMessages}
            onInputChange={setInputMessage}
            onSendMessage={sendMessage}
            onStopGeneration={stopGeneration}
            onKeyPress={handleKeyPress}
            onToolsToggle={onToolsToggle}
            onSelectedToolsChange={setSelectedTools}
            onClearChat={clearCurrentChat}
          />
        ) : (
          <WelcomePage
            models={models}
            selectedModel={selectedModel}
            error={error}
            onCreateConversation={createNewConversation}
          />
        )}
      </div>
    </div>
  );
}