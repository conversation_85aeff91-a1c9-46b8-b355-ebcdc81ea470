'use client';

import React, { useEffect, useRef } from 'react';
// 从 MessageItem 组件文件导入 MessageItem
import { MessageItem } from '@/app/chat/components/MessageItem';
import { ToolCallMessage } from './ToolCallMessage';

import { AIStatusIndicator } from '@/app/chat/components/AIStatusIndicator';
import { Message } from '@/lib/database';
import { AIState } from '../types';

interface ToolCallMessageType {
  id: string;
  type: 'tool_call';
  toolName: string;
  args: any;
  status: 'executing' | 'completed' | 'error';
  result?: string;
  error?: string;
  startTime: number;
  executionTime?: number;
}

interface MessageListProps {
  messages: Message[];
  isStreaming: boolean;
  aiState?: AIState;
  activeToolCalls?: Map<string, any>;
  toolCallMessages: ToolCallMessageType[];
}

export function MessageList({ messages, isStreaming, aiState, activeToolCalls, toolCallMessages }: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isStreaming]);

  // 合并消息和工具调用消息，按时间排序
  const allItems = React.useMemo(() => {
    const items: Array<{
      type: 'message' | 'tool_call';
      data: Message | ToolCallMessageType;
      timestamp: number;
      sequenceNumber: number;
    }> = [];

    // 添加普通消息
    messages.forEach((message, index) => {
      // 确保时间戳的一致性：优先使用timestamp，如果没有则使用created_at转换
      const messageTimestamp = message.timestamp || new Date(message.created_at).getTime();
      items.push({
        type: 'message',
        data: message,
        timestamp: messageTimestamp,
        sequenceNumber: message.sequence_number || index
      });
    });

    // 添加工具调用消息
    toolCallMessages.forEach((toolCall, index) => {
      items.push({
        type: 'tool_call',
        data: toolCall,
        timestamp: toolCall.startTime,
        sequenceNumber: 999999 + index // 工具调用消息使用较大的序列号，确保在同一时间戳下排在消息后面
      });
    });

    // 按时间戳排序，时间戳相同时按序列号排序（与数据库排序逻辑一致）
    return items.sort((a, b) => {
      if (a.timestamp !== b.timestamp) {
        return a.timestamp - b.timestamp;
      }
      return a.sequenceNumber - b.sequenceNumber;
    });
  }, [messages, toolCallMessages]);

  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4 max-h-full">
      {allItems.map((item, index) => {
        if (item.type === 'message') {
          const message = item.data as Message;
          // 过滤掉工具消息，因为工具调用现在由ToolCallMessage组件处理
          if (message.role === 'tool') {
            return null;
          }
          const isLastAssistantMessage = message.role === 'assistant' && 
            index === allItems.length - 1 && 
            aiState && aiState.status !== 'idle';
          
          return (
            <MessageItem 
              key={message.id || `message-${index}`}
              message={message} 
              showAIStatus={isLastAssistantMessage}
              aiState={isLastAssistantMessage ? aiState : undefined}
              activeToolCalls={activeToolCalls}
            />
          );
        } else {
          const toolCall = item.data as ToolCallMessageType;
          return (
            <ToolCallMessage
              key={toolCall.id || `tool-${index}`}
              toolCall={toolCall}
            />
          );
        }
      })}
      
      {/* 显示独立的状态指示器的条件：
          1. 正在流式传输且没有助手消息
          2. 有AI状态且状态不是idle（包括loading、generating等状态）
          3. 特别处理：如果是loading状态，即使有助手消息也要显示
          4. 工具调用状态不在这里显示，而是在消息气泡内显示 */}
      {((isStreaming && !messages.some(msg => msg.role === 'assistant')) || 
        (aiState && aiState.status !== 'idle' && aiState.status !== 'tool_calling' &&
         (!messages.some(msg => msg.role === 'assistant') || aiState.status === 'loading'))) && (
        <AIStatusIndicator aiState={aiState || { status: 'loading', message: '正在准备...' }} />
      )}
      
      <div ref={messagesEndRef} />
    </div>
  );
}