// 导出数据库连接
export { db, initializeDatabase } from './connection';

// 导出所有类型定义
export * from './types';

// 导出各模块的操作函数
export { conversationQueries, conversationOperations } from './conversations';
export { messageQueries, messageOperations } from './messages';
export { mcpServerQueries, mcpServerOperations } from './mcp-servers';
export { mcpToolQueries, mcpToolOperations } from './mcp-tools';
export { mcpToolCallQueries, mcpToolCallOperations } from './mcp-tool-calls';

// 为了保持向后兼容性，重新导出原有的操作对象
import { conversationOperations } from './conversations';
import { messageOperations } from './messages';
import { mcpServerOperations } from './mcp-servers';
import { mcpToolOperations } from './mcp-tools';
import { mcpToolCallOperations } from './mcp-tool-calls';

// 兼容原有的 dbOperations 对象
export const dbOperations = {
  // 对话相关操作
  createConversation: conversationOperations.create,
  getAllConversations: conversationOperations.getAll,
  getConversationById: conversationOperations.getById,
  updateConversationTitle: conversationOperations.updateTitle,
  updateConversationTimestamp: conversationOperations.updateTimestamp,
  deleteConversation: conversationOperations.delete,

  // 消息相关操作
  createMessage: messageOperations.create,
  getMessagesByConversationId: messageOperations.getByConversationId,
  deleteMessagesByConversationId: messageOperations.deleteByConversationId,
  getLastModelByConversationId: messageOperations.getLastModelByConversationId,

  // MCP工具调用相关操作
  getMcpToolCallsByConversationId: mcpToolCallOperations.getByConversationId,
};

// 兼容原有的 mcpDbOperations 对象
export const mcpDbOperations = {
  // MCP服务器相关操作
  createMcpServer: mcpServerOperations.create,
  getAllMcpServers: mcpServerOperations.getAll,
  getMcpServerById: mcpServerOperations.getById,
  getMcpServerByName: mcpServerOperations.getByName,
  getEnabledMcpServers: mcpServerOperations.getEnabled,
  updateMcpServerStatus: mcpServerOperations.updateStatus,
  deleteMcpServer: mcpServerOperations.delete,

  // MCP工具相关操作
  createMcpTool: mcpToolOperations.create,
  getMcpToolsByServerId: mcpToolOperations.getByServerId,
  getMcpToolById: mcpToolOperations.getById,
  getMcpToolByServerIdAndName: mcpToolOperations.getByServerIdAndName,
  getAvailableMcpTools: mcpToolOperations.getAvailable,
  updateMcpToolUsage: mcpToolOperations.updateUsage,
  updateMcpToolAvailability: mcpToolOperations.updateAvailability,
  updateMcpToolEnabled: mcpToolOperations.updateEnabled,
  deleteMcpToolsByServerId: mcpToolOperations.deleteByServerId,
  deleteMcpTool: mcpToolOperations.delete,

  // MCP工具调用相关操作
  createMcpToolCall: mcpToolCallOperations.create,
  getMcpToolCallsByConversationId: mcpToolCallOperations.getByConversationId,
  getMcpToolCallsByToolId: mcpToolCallOperations.getByToolId,
  getRecentMcpToolCalls: mcpToolCallOperations.getRecent,
  getMcpToolCallStats: mcpToolCallOperations.getStats,
  deleteMcpToolCallsByConversationId: mcpToolCallOperations.deleteByConversationId,
  deleteMcpToolCallsByToolId: mcpToolCallOperations.deleteByToolId,
};

// 默认导出数据库连接（保持兼容性）
export { db as default } from './connection';