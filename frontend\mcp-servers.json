{"mcpServers": {"Exa Search": {"type": "streamable-http", "url": "https://server.smithery.ai/exa/mcp?api_key=f249a372-7b6e-4b96-9e5a-050c320e8b18", "path": "/", "protocol": "http", "description": "Fast, intelligent web search and crawling\nExa combines embeddings and traditional search to deliver the best results for LLMs."}, "12306-MCP车票查询工具": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/31679cda84794e/sse", "path": "/", "protocol": "http", "description": "12306-mcp -服务器提供一个简单的API接口，允许用户搜索12306的车票。"}, "Tavily智搜": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/3cce9c7844f042/sse", "path": "/", "protocol": "http", "description": "该服务器使AI系统能够与Tavily的搜索和数据提取工具集成，提供实时的网络信息访问和领域特定的搜索。"}, "Context7": {"type": "streamable-http", "url": "https://server.smithery.ai/@upstash/context7-mcp/mcp?api_key=f249a372-7b6e-4b96-9e5a-050c320e8b18", "path": "/", "protocol": "http", "description": "将特定于版本的最新文档和代码示例直接提取到您的提示中。通过消除过时信息和幻觉 API 来增强您的编码体验。只需将 use context7 添加到您的问题中，即可获得准确且相关的答案。"}, "时间 MCP 服务器": {"type": "sse", "url": "https://mcp.api-inference.modelscope.net/21c27a72682940/sse", "path": "/", "protocol": "http", "description": "一个提供时间和时区转换功能的模型上下文协议服务器。该服务器使 LLM 能够获取当前时间信息，并使用 IANA 时区名称进行时区转换，支持自动检测系统时区。"}}}