'use client';

import { useState, useEffect } from 'react';
import { Conversation, Message } from '@/lib/database';
import { OllamaModel, ChatMessage, AIState } from '@/app/chat/types';
import { ollamaClient } from '@/lib/ollama';

interface ToolCallMessage {
  id: string;
  type: 'tool_call';
  toolName: string;
  args: any;
  status: 'executing' | 'completed' | 'error';
  result?: string;
  error?: string;
  startTime: number;
  executionTime?: number;
}

export interface UseChatReturn {
  // 状态
  models: OllamaModel[];
  conversations: Conversation[];
  currentConversation: Conversation | null;
  messages: Message[];
  selectedModel: string;
  inputMessage: string;
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;
  showModelSelector: boolean;
  enableTools: boolean;
  selectedTools: string[];
  aiState: AIState;
  activeToolCalls: Map<string, any>;
  toolCallMessages: ToolCallMessage[];
  
  // 设置函数
  setSelectedModel: (model: string) => void;
  setInputMessage: (message: string) => void;
  setError: (error: string | null) => void;
  setShowModelSelector: (show: boolean) => void;
  setEnableTools: (enable: boolean) => void;
  setSelectedTools: (tools: string[]) => void;
  
  // 操作函数
  loadConversation: (conversationId: number) => Promise<void>;
  createNewConversation: () => Promise<void>;
  deleteConversation: (conversationId: number) => Promise<void>;
  sendMessage: () => Promise<void>;
  stopGeneration: () => void;
  onToolsToggle: (enabled: boolean) => void;
  clearCurrentChat: () => Promise<void>;
}

export function useChat(): UseChatReturn {
  const [models, setModels] = useState<OllamaModel[]>([]);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedModel, setSelectedModel] = useState<string>('');
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [enableTools, setEnableTools] = useState(false);
  const [selectedTools, setSelectedTools] = useState<string[]>([]);
  const [aiState, setAiState] = useState<AIState>({ status: 'idle' });
  const [abortController, setAbortController] = useState<AbortController | null>(null);
  const [activeToolCalls, setActiveToolCalls] = useState<Map<string, any>>(new Map());
  const [toolCallMessages, setToolCallMessages] = useState<ToolCallMessage[]>([]);

  // 加载模型列表
  const loadModels = async () => {
    try {
      const response = await fetch('/api/models');
      const data = await response.json();
      
      if (data.success) {
        setModels(data.models);
        if (data.models.length > 0 && !selectedModel) {
          setSelectedModel(data.models[0].name);
        }
      } else {
        setError(data.message || '加载模型失败');
      }
    } catch (err) {
      setError('无法连接到服务器');
    }
  };

  // 加载对话列表
  const loadConversations = async () => {
    try {
      const response = await fetch('/api/conversations');
      const data = await response.json();
      
      if (data.success) {
        setConversations(data.conversations);
      }
    } catch (err) {
      console.error('加载对话列表失败:', err);
    }
  };

  // 加载特定对话
  const loadConversation = async (conversationId: number) => {
    try {
      const response = await fetch(`/api/conversations/${conversationId}`);
      const data = await response.json();
      
      if (data.success) {
        setCurrentConversation(data.conversation);
        setMessages(data.messages || []);
        
        // 将数据库中的工具调用记录转换为ToolCallMessage格式
        const toolCallMessages: ToolCallMessage[] = (data.toolCallRecords || []).map((record: any) => ({
          id: record.id.toString(),
          type: 'tool_call' as const,
          toolName: record.tool_name,
          args: record.input_args ? JSON.parse(record.input_args) : {},
          status: record.status === 'success' ? 'completed' as const : 
                  record.status === 'error' ? 'error' as const : 'executing' as const,
          result: record.output_result ? JSON.parse(record.output_result) : undefined,
          error: record.error_message || undefined,
          startTime: new Date(record.created_at).getTime(),
          executionTime: record.execution_time_ms || undefined
        }));
        
        setToolCallMessages(toolCallMessages);
        setActiveToolCalls(new Map());
        
        // 获取对话中最后使用的模型，如果没有则使用对话创建时的模型
        const lastModel = data.lastModel || data.conversation.model;
        setSelectedModel(lastModel);
      }
    } catch (err) {
      setError('加载对话失败');
    }
  };

  // 创建新对话
  const createNewConversation = async () => {
    if (!selectedModel) {
      setError('请先选择一个模型');
      return;
    }

    try {
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: '新对话',
          model: selectedModel,
        }),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setCurrentConversation(data.conversation);
        setMessages([]);
        
        // 清空工具调用消息状态
        setToolCallMessages([]);
        setActiveToolCalls(new Map());
        
        await loadConversations();
      }
    } catch (err) {
      setError('创建对话失败');
    }
  };

  // 删除对话
  const deleteConversation = async (conversationId: number) => {
    if (!confirm('确定要删除这个对话吗？')) {
      return;
    }

    try {
      const response = await fetch(`/api/conversations/${conversationId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        if (currentConversation?.id === conversationId) {
          setCurrentConversation(null);
          setMessages([]);
        }
        await loadConversations();
      }
    } catch (err) {
      setError('删除对话失败');
    }
  };

  // 发送消息
  const sendMessage = async () => {
    if (!inputMessage.trim() || !selectedModel || isStreaming) {
      return;
    }

    if (!currentConversation) {
      await createNewConversation();
      // 等待对话创建完成
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const userMessage: ChatMessage = {
      role: 'user',
      content: inputMessage.trim(),
    };

    // 添加用户消息到界面
    const newUserMessage: Message = {
      id: Date.now(),
      conversation_id: currentConversation?.id || 0,
      role: 'user',
      content: userMessage.content,
      created_at: new Date().toISOString(),
      timestamp: Date.now(), // 确保有正确的时间戳用于排序
    };
    
    setMessages(prev => [...prev, newUserMessage]);
    setInputMessage('');
    setIsStreaming(true);
    setError(null);
    
    // 如果是新对话的第一条消息，清理之前的工具调用消息
    if (messages.length === 0) {
      setToolCallMessages([]);
    }

    try {
      // 准备发送的消息历史
      const chatMessages: ChatMessage[] = [
        ...messages.map(msg => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
        })),
        userMessage,
      ];

      // 检查模型是否已加载，如果未加载则显示加载状态
      try {
        const isLoaded = await ollamaClient.isModelLoaded(selectedModel);
        if (!isLoaded) {
          setAiState({ status: 'loading', message: '正在加载模型...' });
        } else {
          setAiState({ status: 'generating', message: '正在生成回复...' });
        }
      } catch (error) {
        console.error('检查模型加载状态失败:', error);
        // 即使检查失败，也设置为生成状态
        setAiState({ status: 'generating', message: '正在生成回复...' });
      }
      
      const requestBody = {
        model: selectedModel,
        messages: chatMessages,
        conversationId: currentConversation?.id,
        stream: true,
        enableTools: enableTools,
        selectedTools: selectedTools,
      };
      
      console.log('前端发送请求:', requestBody);
      
      // 创建AbortController用于取消请求
      const controller = new AbortController();
      setAbortController(controller);
      
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal: controller.signal,
      });

      if (!response.ok) {
        throw new Error('聊天请求失败');
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      
      if (!reader) {
        throw new Error('无法读取响应');
      }

      // 创建助手消息
      const assistantMessage: Message = {
        id: Date.now() + 1,
        conversation_id: currentConversation?.id || 0,
        role: 'assistant',
        content: '',
        model: selectedModel, // 设置当前选择的模型
        created_at: new Date().toISOString(),
        timestamp: Date.now() + 1, // 确保有正确的时间戳用于排序
      };
      
      setMessages(prev => [...prev, assistantMessage]);
      
      let assistantContent = '';
      let currentAssistantMessageId = assistantMessage.id; // 跟踪当前活跃的消息ID
      
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              setIsStreaming(false);
              setAiState({ status: 'idle' });
              setActiveToolCalls(new Map()); // 清理活跃的工具调用
              await loadConversations(); // 刷新对话列表
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.error) {
                throw new Error(parsed.message || '聊天出错');
              }
              
              // 如果是工具调用相关事件，处理后直接跳过，不添加到AI消息内容中
              if (parsed.type && (parsed.type.startsWith('tool_call_') || parsed.type === 'tool_call')) {
                // 处理工具调用状态事件
              if (parsed.type === 'tool_call_start') {
                setAiState({ status: 'tool_calling', message: '正在执行工具...', toolName: parsed.tool_name });
                
                // 工具调用开始时，结束当前的assistant消息（如果有内容的话）
                if (assistantContent.trim()) {
                  // 当前消息已有内容，保持不变，准备创建新消息用于工具调用后的回复
                  // 重置assistantContent为空，为后续的新消息做准备
                  assistantContent = '';
                }
                
                // 创建新的工具调用消息
                const newToolCallMessage: ToolCallMessage = {
                  id: parsed.tool_call_id || Date.now().toString(),
                  type: 'tool_call',
                  toolName: parsed.tool_name,
                  args: parsed.tool_args,
                  status: 'executing',
                  startTime: Date.now()
                };
                
                setToolCallMessages(prev => {
                  const existing = prev.find(tc => tc.toolName === parsed.tool_name && tc.status === 'executing');
                  if (existing) {
                    return prev.map(tc => 
                      tc.id === existing.id
                        ? { ...tc, args: parsed.tool_args, startTime: Date.now() }
                        : tc
                    );
                  }
                  return [...prev, newToolCallMessage];
                });
                
                // 添加新的工具调用到活跃列表
                setActiveToolCalls(prev => {
                  const newMap = new Map(prev);
                  newMap.set(parsed.tool_call_id, {
                    id: parsed.tool_call_id,
                    toolName: parsed.tool_name,
                    args: parsed.tool_args,
                    status: 'executing',
                    startTime: Date.now()
                  });
                  return newMap;
                });
                
                continue;
              }
              
              if (parsed.type === 'tool_call_complete') {
                setAiState({ status: 'generating', message: '正在生成回复...' });
                
                // 工具调用完成时，创建新的assistant消息用于显示基于工具结果的回复
                const newAssistantMessage: Message = {
                  id: Date.now() + Math.random(), // 确保唯一ID
                  conversation_id: currentConversation?.id || 0,
                  role: 'assistant',
                  content: '',
                  model: selectedModel,
                  created_at: new Date().toISOString(),
                  timestamp: Date.now() + Math.random(), // 确保有正确的时间戳用于排序
                };
                
                setMessages(prev => [...prev, newAssistantMessage]);
                
                // 更新当前活跃的消息ID
                currentAssistantMessageId = newAssistantMessage.id;
                assistantContent = ''; // 重置内容累积器
                
                // 更新工具调用消息状态
                setToolCallMessages(prev => 
                  prev.map(tc => 
                    tc.toolName === parsed.tool_name && tc.status === 'executing'
                      ? { 
                          ...tc, 
                          status: 'completed', 
                          result: parsed.tool_result,
                          executionTime: parsed.execution_time || (Date.now() - tc.startTime)
                        }
                      : tc
                  )
                );
                
                // 更新工具调用状态
                setActiveToolCalls(prev => {
                  const newMap = new Map(prev);
                  const toolCall = newMap.get(parsed.tool_call_id);
                  if (toolCall) {
                    newMap.set(parsed.tool_call_id, {
                      ...toolCall,
                      status: 'completed',
                      result: parsed.tool_result,
                      executionTime: parsed.execution_time
                    });
                  }
                  return newMap;
                });
                
                continue;
              }
              
              if (parsed.type === 'tool_call_error') {
                setAiState({ status: 'generating', message: '正在生成回复...' });
                
                // 更新工具调用消息状态
                setToolCallMessages(prev => 
                  prev.map(tc => 
                    tc.toolName === parsed.tool_name && tc.status === 'executing'
                      ? { 
                          ...tc, 
                          status: 'error', 
                          error: parsed.error_message,
                          executionTime: parsed.execution_time || (Date.now() - tc.startTime)
                        }
                      : tc
                  )
                );
                
                // 更新工具调用状态
                setActiveToolCalls(prev => {
                  const newMap = new Map(prev);
                  const toolCall = newMap.get(parsed.tool_call_id);
                  if (toolCall) {
                    newMap.set(parsed.tool_call_id, {
                      ...toolCall,
                      status: 'error',
                      error: parsed.error_message,
                      executionTime: parsed.execution_time
                    });
                  }
                  return newMap;
                });
                
                continue;
              }
              
              // 所有工具调用相关事件处理完成后，跳过后续处理
              continue;
            }
              
              // 检测内容生成状态
              if (parsed.message?.content) {
                // 有内容输出时，确保处于生成状态
                setAiState({ status: 'generating', message: '正在生成回复...' });
                
                // 智能清理工具调用相关的内容，只清理type为'function'的工具调用
                let cleanContent = parsed.message.content;
                
                // 检测并清理各种工具调用格式
                const functionToolCallRegex = /```json\n{[\s\S]*?"type":\s*["']function["'][\s\S]*?}\n```/g;
                const mcpToolCallRegex = /调用工具:\s*.*?\n参数:\s*[\s\S]*?\n\n(?:结果|错误|状态):\s*[\s\S]*?(?=\n\n|$)/g;
                
                // 清理function类型的工具调用JSON格式
                cleanContent = cleanContent.replace(functionToolCallRegex, '');
                
                // 清理MCP工具调用格式
                cleanContent = cleanContent.replace(mcpToolCallRegex, '');
                
                // 清理其他工具调用相关的状态信息
                cleanContent = cleanContent
                  .replace(/工具调用完成[\s\S]*?(?=\n\n|$)/g, '')
                  .replace(/工具执行结果[\s\S]*?(?=\n\n|$)/g, '')
                  .replace(/🔧\s*.*?\n[\s\S]*?(?=\n\n|$)/g, '') // 清理工具图标开头的内容
                  .replace(/执行工具[\s\S]*?(?=\n\n|$)/g, '')
                  .replace(/Tool\s+call[\s\S]*?(?=\n\n|$)/gi, '')
                  .replace(/\n{3,}/g, '\n\n') // 清理多余的换行
                  .trim();
                
                // 只有在清理后的内容不为空时才添加
                if (cleanContent.trim()) {
                  assistantContent += cleanContent;
                  
                  setMessages(prev => 
                    prev.map(msg => 
                      msg.id === currentAssistantMessageId
                        ? { ...msg, content: assistantContent }
                        : msg
                    )
                  );
                }
              }
              
              // 如果收到了完成标志和统计信息，更新消息的统计数据
              if (parsed.done && (parsed.total_duration || parsed.eval_count || parsed.prompt_eval_count)) {
                setMessages(prev => 
                  prev.map(msg => 
                    msg.id === currentAssistantMessageId
                      ? { 
                          ...msg, 
                          total_duration: parsed.total_duration,
                          load_duration: parsed.load_duration,
                          prompt_eval_count: parsed.prompt_eval_count,
                          prompt_eval_duration: parsed.prompt_eval_duration,
                          eval_count: parsed.eval_count,
                          eval_duration: parsed.eval_duration
                        }
                      : msg
                  )
                );
              }
              
              // 如果模型开始响应（无论之前是否已加载），都切换到生成状态
              else if (parsed.model || parsed.created_at) {
                // 从加载模型切换到生成状态
                setAiState({ status: 'generating', message: '正在生成回复...' });
                
                // 如果收到了模型信息，更新消息的model字段
                if (parsed.model) {
                  setMessages(prev => 
                    prev.map(msg => 
                      msg.id === currentAssistantMessageId
                        ? { ...msg, model: parsed.model }
                        : msg
                    )
                  );
                }
              }
            } catch (parseError) {
              // 忽略解析错误，继续处理下一行
            }
          }
        }
      }
    } catch (err) {
      // 检查是否是用户主动取消的请求
      if (err instanceof Error && err.name === 'AbortError') {
        console.log('请求被用户取消');
        setError(null); // 用户主动取消不显示错误
      } else {
        setError(err instanceof Error ? err.message : '发送消息失败');
        // 移除失败的用户消息
        setMessages(prev => prev.filter(msg => msg.id !== newUserMessage.id));
      }
      setAiState({ status: 'idle' });
    } finally {
      setIsStreaming(false);
      setAiState({ status: 'idle' });
      setActiveToolCalls(new Map()); // 清理活跃的工具调用
      setAbortController(null);
      // 不清理toolCallMessages，让工具调用消息持久显示
    }
  };

  // 停止生成
  const stopGeneration = () => {
    if (abortController) {
      abortController.abort();
      setAbortController(null);
      setIsStreaming(false);
      setAiState({ status: 'idle' });
      setActiveToolCalls(new Map()); // 清理活跃的工具调用
      // 不清理toolCallMessages，让工具调用消息持久显示
    }
  };

  // 清空当前对话
  const clearCurrentChat = async () => {
    if (!currentConversation) {
      return;
    }

    if (!confirm('确定要清空当前对话的所有消息吗？此操作不可撤销。')) {
      return;
    }

    try {
      const response = await fetch(`/api/conversations/${currentConversation.id}/clear`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        // 清空本地消息状态
        setMessages([]);
        setError(null);
      } else {
        const data = await response.json();
        setError(data.error || '清空对话失败');
      }
    } catch (err) {
      setError('清空对话失败');
    }
  };

  // 初始化
  useEffect(() => {
    loadModels();
    loadConversations();
  }, []);

  const handleToolsToggle = (enabled: boolean) => {
    setEnableTools(enabled);
  };

  // 处理模型切换
  const handleModelChange = (model: string) => {
    setSelectedModel(model);
    // 模型切换时不立即检查加载状态，只在发送消息时才检查
    // 这样避免在选择模型时就显示加载动画
    setAiState({ status: 'idle' });
  };

  return {
    // 状态
    models,
    conversations,
    currentConversation,
    messages,
    selectedModel,
    inputMessage,
    isLoading,
    isStreaming,
    error,
    showModelSelector,
    enableTools,
    selectedTools,
    aiState,
    activeToolCalls,
    toolCallMessages,
    
    // 设置函数
    setSelectedModel: handleModelChange,
    setInputMessage,
    setError,
    setShowModelSelector,
    setEnableTools,
    setSelectedTools,
    
    // 操作函数
    loadConversation,
    createNewConversation,
    deleteConversation,
    sendMessage,
    stopGeneration,
    onToolsToggle: handleToolsToggle,
    clearCurrentChat,
  };
}