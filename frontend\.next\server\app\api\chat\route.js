/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\api\\\\chat\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_ollama__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/ollama */ \"(rsc)/./src/lib/ollama.ts\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../lib/database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _lib_tools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../lib/tools */ \"(rsc)/./src/lib/tools.ts\");\n/* harmony import */ var _lib_mcp_mcp_client_server__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../lib/mcp/mcp-client-server */ \"(rsc)/./src/lib/mcp/mcp-client-server.ts\");\n\n\n\n\n\nasync function POST(request) {\n    try {\n        // 确保MCP服务器客户端已连接\n        if (!_lib_mcp_mcp_client_server__WEBPACK_IMPORTED_MODULE_4__.mcpServerClient.isClientConnected()) {\n            await _lib_mcp_mcp_client_server__WEBPACK_IMPORTED_MODULE_4__.mcpServerClient.connect();\n        }\n        const body = await request.json();\n        const { model, messages, conversationId, stream = true, options = {}, enableTools = true, testMode = false, selectedTools = [] } = body;\n        // 测试模式：仅验证模型是否支持工具，使用简单的测试工具\n        if (testMode) {\n            try {\n                await _lib_ollama__WEBPACK_IMPORTED_MODULE_1__.ollamaClient.chat({\n                    model,\n                    messages: [\n                        {\n                            role: 'user',\n                            content: 'test'\n                        }\n                    ],\n                    tools: enableTools ? [\n                        _lib_tools__WEBPACK_IMPORTED_MODULE_3__.testTool\n                    ] : undefined,\n                    stream: false\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    supportsTools: true\n                });\n            } catch (error) {\n                const errorMessage = error.message || '';\n                if (errorMessage.includes('does not support tools')) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        supportsTools: false\n                    });\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: false,\n                    error: errorMessage\n                });\n            }\n        }\n        // 验证必需参数\n        if (!model || !messages || !Array.isArray(messages) || messages.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '缺少必需参数: model 和 messages'\n            }, {\n                status: 400\n            });\n        }\n        // 检查Ollama服务是否可用\n        const isAvailable = await _lib_ollama__WEBPACK_IMPORTED_MODULE_1__.ollamaClient.isAvailable();\n        if (!isAvailable) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Ollama服务不可用',\n                message: '请确保Ollama正在运行并监听在localhost:11434端口'\n            }, {\n                status: 503\n            });\n        }\n        // 如果是流式响应\n        if (stream) {\n            const encoder = new TextEncoder();\n            const readableStream = new ReadableStream({\n                async start (controller) {\n                    try {\n                        let assistantMessage = '';\n                        let toolCallsToSave = [];\n                        // 准备聊天请求，如果启用工具则添加用户选择的工具定义\n                        let userSelectedTools = [];\n                        if (enableTools && selectedTools.length > 0) {\n                            userSelectedTools = await (0,_lib_tools__WEBPACK_IMPORTED_MODULE_3__.getToolsByNames)(selectedTools);\n                            console.log('用户选择的工具:', selectedTools);\n                            console.log('获取到的工具定义:', userSelectedTools);\n                        }\n                        let chatRequest = {\n                            model,\n                            messages,\n                            stream: true,\n                            options,\n                            ...enableTools && userSelectedTools.length > 0 && {\n                                tools: userSelectedTools\n                            }\n                        };\n                        console.log('发送聊天请求:', JSON.stringify(chatRequest, null, 2));\n                        console.log('enableTools:', enableTools, 'selectedTools:', selectedTools, 'userSelectedTools:', userSelectedTools);\n                        let retryWithoutTools = false;\n                        try {\n                            // 在开始流式处理前先保存用户消息\n                            if (conversationId) {\n                                const lastUserMessage = messages[messages.length - 1];\n                                if (lastUserMessage?.role === 'user') {\n                                    _lib_database__WEBPACK_IMPORTED_MODULE_2__.dbOperations.createMessage({\n                                        conversation_id: conversationId,\n                                        role: 'user',\n                                        content: lastUserMessage.content,\n                                        model: model\n                                    });\n                                }\n                            }\n                            // 使用流式API\n                            for await (const chunk of _lib_ollama__WEBPACK_IMPORTED_MODULE_1__.ollamaClient.chatStream(chatRequest)){\n                                // 处理工具调用\n                                if (chunk.message?.tool_calls && chunk.message.tool_calls.length > 0) {\n                                    // 执行工具调用\n                                    for (const toolCall of chunk.message.tool_calls){\n                                        const startTime = Date.now();\n                                        let toolCallMessageId = null;\n                                        let toolResultMessageId = null;\n                                        let mcpToolCallId = null;\n                                        try {\n                                            // 安全解析工具调用参数\n                                            let args = {};\n                                            if (toolCall.function.arguments) {\n                                                if (typeof toolCall.function.arguments === 'string') {\n                                                    try {\n                                                        args = JSON.parse(toolCall.function.arguments);\n                                                    } catch (parseError) {\n                                                        console.error('工具参数JSON解析失败:', parseError, '原始参数:', toolCall.function.arguments);\n                                                        throw new Error(`工具参数格式错误: ${toolCall.function.arguments}`);\n                                                    }\n                                                } else if (typeof toolCall.function.arguments === 'object') {\n                                                    args = toolCall.function.arguments;\n                                                }\n                                            }\n                                            // 发送工具调用开始状态\n                                            const toolStartData = `data: ${JSON.stringify({\n                                                type: 'tool_call_start',\n                                                tool_name: toolCall.function.name,\n                                                tool_args: args,\n                                                tool_call_id: toolCall.id\n                                            })}\\n\\n`;\n                                            controller.enqueue(encoder.encode(toolStartData));\n                                            // 从用户选择的工具中查找对应的serverName\n                                            const selectedTool = userSelectedTools.find((tool)=>tool.function.name === toolCall.function.name);\n                                            const serverName = selectedTool?.serverName;\n                                            console.log(`执行工具 ${toolCall.function.name}，使用服务器: ${serverName || '自动检测'}`);\n                                            const result = await _lib_tools__WEBPACK_IMPORTED_MODULE_3__.ToolExecutor.executeToolCall(toolCall.function.name, args, serverName);\n                                            const executionTime = Date.now() - startTime;\n                                            // 工具调用结果现在通过独立的ToolCallMessage组件显示，不再添加到助手消息中\n                                            // 暂存工具调用信息，稍后关联到助手消息\n                                            if (conversationId) {\n                                                try {\n                                                    const availableTools = _lib_database__WEBPACK_IMPORTED_MODULE_2__.mcpDbOperations.getAvailableMcpTools();\n                                                    const mcpTool = availableTools.find((tool)=>tool.name === toolCall.function.name);\n                                                    if (mcpTool) {\n                                                        // 将工具调用信息存储到临时数组中，等助手消息保存后再关联\n                                                        if (!toolCallsToSave) {\n                                                            toolCallsToSave = [];\n                                                        }\n                                                        toolCallsToSave.push({\n                                                            tool_id: mcpTool.id,\n                                                            input_args: args,\n                                                            output_result: {\n                                                                result\n                                                            },\n                                                            execution_time_ms: executionTime,\n                                                            status: 'success'\n                                                        });\n                                                    }\n                                                } catch (dbError) {\n                                                    console.error('准备MCP工具调用记录失败:', dbError);\n                                                }\n                                            }\n                                            // 发送工具调用完成状态\n                                            const toolCompleteData = `data: ${JSON.stringify({\n                                                type: 'tool_call_complete',\n                                                tool_name: toolCall.function.name,\n                                                tool_args: args,\n                                                tool_result: result,\n                                                tool_call_id: toolCall.id,\n                                                execution_time: executionTime\n                                            })}\\n\\n`;\n                                            controller.enqueue(encoder.encode(toolCompleteData));\n                                            // 工具调用结果已通过tool_call_complete事件发送，无需重复发送\n                                            // 将工具结果添加到消息历史中，继续对话\n                                            const updatedMessages = [\n                                                ...messages,\n                                                {\n                                                    role: 'assistant',\n                                                    content: '',\n                                                    tool_calls: [\n                                                        toolCall\n                                                    ]\n                                                },\n                                                {\n                                                    role: 'tool',\n                                                    content: result\n                                                }\n                                            ];\n                                            // 继续对话以获取基于工具结果的回复\n                                            const followUpRequest = {\n                                                model,\n                                                messages: updatedMessages,\n                                                stream: true,\n                                                options\n                                            };\n                                            let followUpMessage = '';\n                                            for await (const followUpChunk of _lib_ollama__WEBPACK_IMPORTED_MODULE_1__.ollamaClient.chatStream(followUpRequest)){\n                                                if (followUpChunk.message?.content) {\n                                                    followUpMessage += followUpChunk.message.content;\n                                                }\n                                                const followUpData = `data: ${JSON.stringify(followUpChunk)}\\n\\n`;\n                                                controller.enqueue(encoder.encode(followUpData));\n                                                if (followUpChunk.done && conversationId && followUpMessage.trim()) {\n                                                    // 为工具调用后的回复创建单独的消息\n                                                    _lib_database__WEBPACK_IMPORTED_MODULE_2__.dbOperations.createMessage({\n                                                        conversation_id: conversationId,\n                                                        role: 'assistant',\n                                                        content: followUpMessage,\n                                                        model: model,\n                                                        total_duration: followUpChunk.total_duration,\n                                                        load_duration: followUpChunk.load_duration,\n                                                        prompt_eval_count: followUpChunk.prompt_eval_count,\n                                                        prompt_eval_duration: followUpChunk.prompt_eval_duration,\n                                                        eval_count: followUpChunk.eval_count,\n                                                        eval_duration: followUpChunk.eval_duration\n                                                    });\n                                                    break;\n                                                }\n                                            }\n                                        } catch (toolError) {\n                                            console.error('工具执行失败:', toolError);\n                                            const executionTime = Date.now() - startTime;\n                                            const errorMessage = toolError instanceof Error ? toolError.message : '未知错误';\n                                            // 重新解析参数用于错误显示\n                                            let errorArgs = {};\n                                            try {\n                                                if (toolCall.function.arguments) {\n                                                    errorArgs = typeof toolCall.function.arguments === 'string' ? JSON.parse(toolCall.function.arguments) : toolCall.function.arguments;\n                                                }\n                                            } catch  {\n                                                errorArgs = {\n                                                    raw: toolCall.function.arguments\n                                                };\n                                            }\n                                            // 发送工具调用错误状态\n                                            const toolErrorData = `data: ${JSON.stringify({\n                                                type: 'tool_call_error',\n                                                tool_name: toolCall.function.name,\n                                                tool_args: errorArgs,\n                                                error_message: errorMessage,\n                                                tool_call_id: toolCall.id,\n                                                execution_time: executionTime\n                                            })}\\n\\n`;\n                                            controller.enqueue(encoder.encode(toolErrorData));\n                                            // 工具执行错误现在通过独立的ToolCallMessage组件显示，不再添加到助手消息中\n                                            // 暂存失败的工具调用信息\n                                            if (conversationId) {\n                                                try {\n                                                    const availableTools = _lib_database__WEBPACK_IMPORTED_MODULE_2__.mcpDbOperations.getAvailableMcpTools();\n                                                    const mcpTool = availableTools.find((tool)=>tool.name === toolCall.function.name);\n                                                    if (mcpTool) {\n                                                        // 安全解析参数，如果解析失败则使用原始字符串\n                                                        let inputArgs = {};\n                                                        try {\n                                                            if (toolCall.function.arguments) {\n                                                                inputArgs = typeof toolCall.function.arguments === 'string' ? JSON.parse(toolCall.function.arguments) : toolCall.function.arguments;\n                                                            }\n                                                        } catch (parseError) {\n                                                            console.error('解析工具参数失败，使用原始字符串:', parseError);\n                                                            inputArgs = toolCall.function.arguments;\n                                                        }\n                                                        // 将失败的工具调用信息存储到临时数组中\n                                                        if (!toolCallsToSave) {\n                                                            toolCallsToSave = [];\n                                                        }\n                                                        toolCallsToSave.push({\n                                                            tool_id: mcpTool.id,\n                                                            input_args: inputArgs,\n                                                            output_result: undefined,\n                                                            execution_time_ms: executionTime,\n                                                            status: 'error',\n                                                            error_message: errorMessage\n                                                        });\n                                                    }\n                                                } catch (dbError) {\n                                                    console.error('准备失败的MCP工具调用记录失败:', dbError);\n                                                }\n                                            }\n                                            const errorResult = `工具执行失败: ${errorMessage}`;\n                                            const toolResultErrorData = `data: ${JSON.stringify({\n                                                message: {\n                                                    role: 'tool',\n                                                    content: errorResult,\n                                                    tool_call_id: toolCall.id\n                                                }\n                                            })}\\n\\n`;\n                                            controller.enqueue(encoder.encode(toolResultErrorData));\n                                        }\n                                    }\n                                } else {\n                                    // 累积助手的回复内容\n                                    if (chunk.message?.content) {\n                                        assistantMessage += chunk.message.content;\n                                    }\n                                    // 发送数据块到客户端\n                                    const data = `data: ${JSON.stringify(chunk)}\\n\\n`;\n                                    controller.enqueue(encoder.encode(data));\n                                    // 如果完成，保存助手消息到数据库\n                                    if (chunk.done && conversationId && assistantMessage.trim()) {\n                                        try {\n                                            // 保存助手回复，包含统计信息\n                                            const assistantMessageId = _lib_database__WEBPACK_IMPORTED_MODULE_2__.dbOperations.createMessage({\n                                                conversation_id: conversationId,\n                                                role: 'assistant',\n                                                content: assistantMessage,\n                                                model: model,\n                                                total_duration: chunk.total_duration,\n                                                load_duration: chunk.load_duration,\n                                                prompt_eval_count: chunk.prompt_eval_count,\n                                                prompt_eval_duration: chunk.prompt_eval_duration,\n                                                eval_count: chunk.eval_count,\n                                                eval_duration: chunk.eval_duration\n                                            });\n                                            // 保存关联的工具调用记录\n                                            if (toolCallsToSave.length > 0) {\n                                                for (const toolCallData of toolCallsToSave){\n                                                    try {\n                                                        _lib_database__WEBPACK_IMPORTED_MODULE_2__.mcpDbOperations.createMcpToolCall({\n                                                            ...toolCallData,\n                                                            conversation_id: conversationId,\n                                                            message_id: assistantMessageId\n                                                        });\n                                                    } catch (dbError) {\n                                                        console.error('保存MCP工具调用记录失败:', dbError);\n                                                    }\n                                                }\n                                                toolCallsToSave = []; // 清空临时数组\n                                            }\n                                        } catch (dbError) {\n                                            console.error('保存消息到数据库失败:', dbError);\n                                        }\n                                    }\n                                }\n                            }\n                            // 发送结束标志\n                            controller.enqueue(encoder.encode('data: [DONE]\\n\\n'));\n                            controller.close();\n                        } catch (streamError) {\n                            console.error('流式请求错误:', streamError);\n                            // 检查是否是模型不支持工具的错误\n                            const errorMessage = streamError instanceof Error ? streamError.message : String(streamError);\n                            const isToolsNotSupported = errorMessage.includes('does not support tools');\n                            // 如果启用了工具且出现工具不支持错误，尝试不使用工具重新请求\n                            if (enableTools && !retryWithoutTools && isToolsNotSupported) {\n                                console.log('模型不支持工具调用，尝试不使用工具重新请求');\n                                retryWithoutTools = true;\n                                // 重置助手消息内容，避免重复累积\n                                assistantMessage = '';\n                                // 重新构建不包含工具的请求\n                                chatRequest = {\n                                    model,\n                                    messages,\n                                    stream: true,\n                                    options\n                                };\n                                // 重新尝试流式API\n                                for await (const chunk of _lib_ollama__WEBPACK_IMPORTED_MODULE_1__.ollamaClient.chatStream(chatRequest)){\n                                    // 累积助手的回复内容\n                                    if (chunk.message?.content) {\n                                        assistantMessage += chunk.message.content;\n                                    }\n                                    // 发送数据块到客户端\n                                    const data = `data: ${JSON.stringify(chunk)}\\n\\n`;\n                                    controller.enqueue(encoder.encode(data));\n                                    // 如果完成，保存助手消息到数据库\n                                    if (chunk.done && conversationId && assistantMessage.trim()) {\n                                        try {\n                                            // 保存助手回复\n                                            _lib_database__WEBPACK_IMPORTED_MODULE_2__.dbOperations.createMessage({\n                                                conversation_id: conversationId,\n                                                role: 'assistant',\n                                                content: assistantMessage,\n                                                model: model\n                                            });\n                                        } catch (dbError) {\n                                            console.error('保存消息到数据库失败:', dbError);\n                                        }\n                                    }\n                                }\n                                // 发送结束标志\n                                controller.enqueue(encoder.encode('data: [DONE]\\n\\n'));\n                                controller.close();\n                            } else {\n                                // 如果已经重试过或者没有启用工具，或者不是工具不支持的错误，直接抛出错误\n                                throw streamError;\n                            }\n                        }\n                    } catch (error) {\n                        console.error('流式聊天失败:', error);\n                        const errorData = `data: ${JSON.stringify({\n                            error: true,\n                            message: error instanceof Error ? error.message : '聊天请求失败'\n                        })}\\n\\n`;\n                        controller.enqueue(encoder.encode(errorData));\n                        controller.close();\n                    }\n                }\n            });\n            return new Response(readableStream, {\n                headers: {\n                    'Content-Type': 'text/event-stream',\n                    'Cache-Control': 'no-cache',\n                    'Connection': 'keep-alive'\n                }\n            });\n        } else {\n            // 非流式响应\n            const userSelectedTools = enableTools && selectedTools.length > 0 ? await (0,_lib_tools__WEBPACK_IMPORTED_MODULE_3__.getToolsByNames)(selectedTools) : [];\n            const chatRequest = {\n                model,\n                messages,\n                stream: false,\n                options,\n                ...enableTools && userSelectedTools.length > 0 && {\n                    tools: userSelectedTools\n                }\n            };\n            console.log('非流式聊天请求:', JSON.stringify(chatRequest, null, 2));\n            console.log('非流式 enableTools:', enableTools, 'selectedTools:', selectedTools, 'userSelectedTools:', userSelectedTools);\n            let response = await _lib_ollama__WEBPACK_IMPORTED_MODULE_1__.ollamaClient.chat(chatRequest);\n            let finalMessages = [\n                ...messages\n            ];\n            // 处理工具调用\n            if (response.message?.tool_calls && response.message.tool_calls.length > 0) {\n                for (const toolCall of response.message.tool_calls){\n                    const startTime = Date.now();\n                    let toolCallMessageId = null;\n                    let toolResultMessageId = null;\n                    try {\n                        const args = JSON.parse(toolCall.function.arguments);\n                        // 保存工具调用消息到数据库\n                        if (conversationId) {\n                            try {\n                                toolCallMessageId = _lib_database__WEBPACK_IMPORTED_MODULE_2__.dbOperations.createMessage({\n                                    conversation_id: conversationId,\n                                    role: 'assistant',\n                                    content: `调用工具: ${toolCall.function.name}\\n参数: ${JSON.stringify(args, null, 2)}`,\n                                    model: model\n                                });\n                            } catch (dbError) {\n                                console.error('保存工具调用消息失败:', dbError);\n                            }\n                        }\n                        // 从用户选择的工具中查找对应的serverName\n                        const selectedTool = userSelectedTools.find((tool)=>tool.function.name === toolCall.function.name);\n                        const serverName = selectedTool?.serverName;\n                        console.log(`非流式执行工具 ${toolCall.function.name}，使用服务器: ${serverName || '自动检测'}`);\n                        const result = await _lib_tools__WEBPACK_IMPORTED_MODULE_3__.ToolExecutor.executeToolCall(toolCall.function.name, args, serverName);\n                        const executionTime = Date.now() - startTime;\n                        // 保存工具结果消息到数据库\n                        if (conversationId) {\n                            try {\n                                toolResultMessageId = _lib_database__WEBPACK_IMPORTED_MODULE_2__.dbOperations.createMessage({\n                                    conversation_id: conversationId,\n                                    role: 'tool',\n                                    content: result,\n                                    model: model\n                                });\n                            } catch (dbError) {\n                                console.error('保存工具结果消息失败:', dbError);\n                            }\n                        }\n                        // 保存MCP工具调用记录\n                        if (conversationId) {\n                            try {\n                                const availableTools = _lib_database__WEBPACK_IMPORTED_MODULE_2__.mcpDbOperations.getAvailableMcpTools();\n                                const mcpTool = availableTools.find((tool)=>tool.name === toolCall.function.name);\n                                if (mcpTool) {\n                                // 工具调用记录将在助手消息保存时统一处理\n                                }\n                            } catch (dbError) {\n                                console.error('保存MCP工具调用记录失败:', dbError);\n                            }\n                        }\n                        // 添加工具调用和结果到消息历史\n                        finalMessages.push({\n                            role: 'assistant',\n                            content: '',\n                            tool_calls: [\n                                toolCall\n                            ]\n                        });\n                        finalMessages.push({\n                            role: 'tool',\n                            content: result\n                        });\n                        // 继续对话以获取基于工具结果的回复\n                        const followUpResponse = await _lib_ollama__WEBPACK_IMPORTED_MODULE_1__.ollamaClient.chat({\n                            model,\n                            messages: finalMessages,\n                            stream: false,\n                            options\n                        });\n                        response = followUpResponse;\n                    } catch (toolError) {\n                        console.error('工具执行失败:', toolError);\n                        const errorMessage = toolError instanceof Error ? toolError.message : '未知错误';\n                        // 保存工具调用失败记录到数据库\n                        if (conversationId && response.message?.tool_calls?.[0]) {\n                            const toolCall = response.message.tool_calls[0];\n                            try {\n                                // 保存错误结果消息\n                                const toolResultMessageId = _lib_database__WEBPACK_IMPORTED_MODULE_2__.dbOperations.createMessage({\n                                    conversation_id: conversationId,\n                                    role: 'tool',\n                                    content: `工具执行失败: ${errorMessage}`,\n                                    model: model\n                                });\n                                // 保存MCP工具调用记录\n                                const availableTools = _lib_database__WEBPACK_IMPORTED_MODULE_2__.mcpDbOperations.getAvailableMcpTools();\n                                const mcpTool = availableTools.find((tool)=>tool.name === toolCall.function.name);\n                                if (mcpTool) {\n                                    let inputArgs = {};\n                                    try {\n                                        if (toolCall.function.arguments) {\n                                            inputArgs = typeof toolCall.function.arguments === 'string' ? JSON.parse(toolCall.function.arguments) : toolCall.function.arguments;\n                                        }\n                                    } catch (parseError) {\n                                        inputArgs = {\n                                            raw_arguments: toolCall.function.arguments\n                                        };\n                                    }\n                                // 工具调用记录将在助手消息保存时统一处理\n                                }\n                            } catch (dbError) {\n                                console.error('保存工具调用失败记录失败:', dbError);\n                            }\n                        }\n                        // 在工具执行失败时，返回错误信息\n                        response.message.content = `工具执行失败: ${errorMessage}`;\n                    }\n                }\n            }\n            // 保存消息到数据库\n            if (conversationId) {\n                try {\n                    // 保存用户消息\n                    const lastUserMessage = messages[messages.length - 1];\n                    if (lastUserMessage?.role === 'user') {\n                        _lib_database__WEBPACK_IMPORTED_MODULE_2__.dbOperations.createMessage({\n                            conversation_id: conversationId,\n                            role: 'user',\n                            content: lastUserMessage.content,\n                            model: model\n                        });\n                    }\n                    // 保存助手回复\n                    if (response.message?.content) {\n                        _lib_database__WEBPACK_IMPORTED_MODULE_2__.dbOperations.createMessage({\n                            conversation_id: conversationId,\n                            role: 'assistant',\n                            content: response.message.content,\n                            model: model\n                        });\n                    }\n                } catch (dbError) {\n                    console.error('保存消息到数据库失败:', dbError);\n                }\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                response\n            });\n        }\n    } catch (error) {\n        console.error('ollama.ts chatStream 详细错误:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '聊天请求失败',\n            message: error instanceof Error ? `流式聊天请求失败: ${error.message}` : '流式聊天请求失败，请检查网络连接和Ollama服务状态'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.conversationOperations),\n/* harmony export */   conversationQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.conversationQueries),\n/* harmony export */   db: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   dbOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.dbOperations),\n/* harmony export */   \"default\": () => (/* reexport safe */ _database_connection__WEBPACK_IMPORTED_MODULE_1__.db),\n/* harmony export */   initializeDatabase: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase),\n/* harmony export */   mcpDbOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpDbOperations),\n/* harmony export */   mcpServerOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpServerQueries),\n/* harmony export */   mcpToolCallOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolCallOperations),\n/* harmony export */   mcpToolCallQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolCallQueries),\n/* harmony export */   mcpToolOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolQueries),\n/* harmony export */   messageOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.messageOperations),\n/* harmony export */   messageQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.messageQueries)\n/* harmony export */ });\n/* harmony import */ var _database_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/index */ \"(rsc)/./src/lib/database/index.ts\");\n/* harmony import */ var _database_connection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database/connection */ \"(rsc)/./src/lib/database/connection.ts\");\n// 重新导出模块化的数据库操作\n// 这个文件现在作为向后兼容的入口点，所有实际的实现都在 ./database/ 目录下\n\n// 重新导出数据库连接作为默认导出（保持向后兼容性）\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsZ0JBQWdCO0FBQ2hCLDZDQUE2QztBQUNaO0FBRWpDLDJCQUEyQjtBQUMyQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcc3JjXFxsaWJcXGRhdGFiYXNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIOmHjeaWsOWvvOWHuuaooeWdl+WMlueahOaVsOaNruW6k+aTjeS9nFxuLy8g6L+Z5Liq5paH5Lu2546w5Zyo5L2c5Li65ZCR5ZCO5YW85a6555qE5YWl5Y+j54K577yM5omA5pyJ5a6e6ZmF55qE5a6e546w6YO95ZyoIC4vZGF0YWJhc2UvIOebruW9leS4i1xuZXhwb3J0ICogZnJvbSAnLi9kYXRhYmFzZS9pbmRleCc7XG5cbi8vIOmHjeaWsOWvvOWHuuaVsOaNruW6k+i/nuaOpeS9nOS4uum7mOiupOWvvOWHuu+8iOS/neaMgeWQkeWQjuWFvOWuueaAp++8iVxuZXhwb3J0IHsgZGIgYXMgZGVmYXVsdCB9IGZyb20gJy4vZGF0YWJhc2UvY29ubmVjdGlvbic7Il0sIm5hbWVzIjpbImRiIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connection.ts":
/*!****************************************!*\
  !*** ./src/lib/database/connection.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(better_sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// 数据库连接配置\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'chat.db');\nconst db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default())(dbPath);\n// 数据库初始化SQL\nconst initializeDatabase = ()=>{\n    const initSQL = `\n    CREATE TABLE IF NOT EXISTS conversations (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      model TEXT NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    );\n\n    CREATE TABLE IF NOT EXISTS messages (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      conversation_id INTEGER NOT NULL,\n      role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system', 'tool')),\n      content TEXT NOT NULL,\n      model TEXT,\n      sequence_number INTEGER DEFAULT 0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      timestamp INTEGER, -- 毫秒级时间戳，用于精确排序\n      -- Ollama生成统计信息\n      total_duration INTEGER,\n      load_duration INTEGER,\n      prompt_eval_count INTEGER,\n      prompt_eval_duration INTEGER,\n      eval_count INTEGER,\n      eval_duration INTEGER,\n      FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE\n    );\n\n    -- MCP服务器统一配置表\n    CREATE TABLE IF NOT EXISTS mcp_servers (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      display_name TEXT NOT NULL,\n      description TEXT,\n      type TEXT NOT NULL CHECK (type IN ('stdio', 'sse', 'streamable-http')),\n      status TEXT NOT NULL DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'connecting')),\n      enabled BOOLEAN NOT NULL DEFAULT 1,\n      \n      -- STDIO配置\n      command TEXT,\n      args TEXT, -- JSON数组格式\n      working_directory TEXT,\n      \n      -- SSE/HTTP配置\n      url TEXT,\n      base_url TEXT,\n      port INTEGER,\n      path TEXT DEFAULT '/',\n      protocol TEXT DEFAULT 'http' CHECK (protocol IN ('http', 'https')),\n      \n      -- 通用配置\n      headers TEXT, -- JSON对象格式\n      auth_type TEXT CHECK (auth_type IN ('none', 'bearer', 'basic', 'api_key')),\n      auth_config TEXT, -- JSON格式\n      timeout_ms INTEGER DEFAULT 30000,\n      retry_attempts INTEGER DEFAULT 3,\n      retry_delay_ms INTEGER DEFAULT 1000,\n      \n      -- 扩展配置\n      extra_config TEXT, -- JSON格式，存储其他特殊配置\n      \n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      last_connected_at DATETIME,\n      error_message TEXT\n    );\n\n    -- MCP工具表\n    CREATE TABLE IF NOT EXISTS mcp_tools (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      server_id INTEGER NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT,\n      input_schema TEXT, -- JSON格式存储工具的输入参数模式\n      is_available BOOLEAN DEFAULT 1,\n      enabled BOOLEAN DEFAULT 1, -- 工具是否启用（在对话页面可见）\n      last_used_at DATETIME,\n      usage_count INTEGER DEFAULT 0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (server_id) REFERENCES mcp_servers (id) ON DELETE CASCADE,\n      UNIQUE(server_id, name)\n    );\n\n    -- MCP工具调用记录表\n    CREATE TABLE IF NOT EXISTS mcp_tool_calls (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      tool_id INTEGER NOT NULL,\n      conversation_id INTEGER NOT NULL, -- 关联到对话\n      message_id INTEGER, -- 关联到具体的消息（可选）\n      input_args TEXT, -- JSON格式存储输入参数\n      output_result TEXT, -- JSON格式存储输出结果\n      execution_time_ms INTEGER,\n      status TEXT NOT NULL CHECK (status IN ('success', 'error', 'timeout')),\n      error_message TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (tool_id) REFERENCES mcp_tools (id) ON DELETE CASCADE,\n      FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE,\n      FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE SET NULL\n    );\n\n    -- 原有索引\n    CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);\n    CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);\n    \n    -- MCP相关索引\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_type ON mcp_servers(type);\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_status ON mcp_servers(status);\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_enabled ON mcp_servers(enabled);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_server_id ON mcp_tools(server_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(name);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_available ON mcp_tools(is_available);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conversation_id ON mcp_tool_calls(conversation_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_tool_id ON mcp_tool_calls(tool_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_message_id ON mcp_tool_calls(message_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_created_at ON mcp_tool_calls(created_at DESC);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_status ON mcp_tool_calls(status);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conv_time ON mcp_tool_calls(conversation_id, created_at DESC);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conv_status ON mcp_tool_calls(conversation_id, status);\n  `;\n    // 执行初始化SQL\n    db.exec(initSQL);\n    // 数据库迁移：为messages表添加timestamp列（如果不存在）\n    try {\n        // 检查timestamp列是否存在\n        const columns = db.prepare(\"PRAGMA table_info(messages)\").all();\n        const hasTimestamp = columns.some((col)=>col.name === 'timestamp');\n        if (!hasTimestamp) {\n            console.log('正在为messages表添加timestamp列...');\n            db.exec('ALTER TABLE messages ADD COLUMN timestamp INTEGER');\n            // 为现有消息设置timestamp（基于created_at）\n            const updateTimestamp = db.prepare(`\n        UPDATE messages \n        SET timestamp = CAST((julianday(created_at) - 2440587.5) * 86400000 AS INTEGER)\n        WHERE timestamp IS NULL\n      `);\n            updateTimestamp.run();\n            console.log('messages表timestamp列添加完成');\n        }\n    } catch (error) {\n        console.error('数据库迁移失败:', error);\n    }\n    console.log('数据库初始化完成');\n    // 运行时间戳迁移（如果需要）\n    try {\n        const { autoMigrateIfNeeded } = __webpack_require__(/*! ./migrate-timestamps */ \"(rsc)/./src/lib/database/migrate-timestamps.ts\");\n        autoMigrateIfNeeded();\n    } catch (error) {\n        console.error('时间戳迁移失败:', error);\n    }\n};\n// 初始化数据库\ninitializeDatabase();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (db);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connection.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/conversations.ts":
/*!*******************************************!*\
  !*** ./src/lib/database/conversations.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationOperations: () => (/* binding */ conversationOperations),\n/* harmony export */   conversationQueries: () => (/* binding */ conversationQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// 对话相关查询语句\nconst conversationQueries = {\n    // 创建新对话\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO conversations (title, model)\n    VALUES (?, ?)\n  `),\n    // 获取所有对话\n    getAll: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM conversations\n    ORDER BY updated_at DESC\n  `),\n    // 根据ID获取对话\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM conversations\n    WHERE id = ?\n  `),\n    // 更新对话标题\n    updateTitle: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE conversations\n    SET title = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新对话的最后更新时间\n    updateTimestamp: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE conversations\n    SET updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除对话\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM conversations\n    WHERE id = ?\n  `)\n};\n// 对话数据库操作函数\nconst conversationOperations = {\n    // 创建新对话\n    create (data) {\n        const result = conversationQueries.create.run(data.title, data.model);\n        return result.lastInsertRowid;\n    },\n    // 获取所有对话\n    getAll () {\n        return conversationQueries.getAll.all();\n    },\n    // 根据ID获取对话\n    getById (id) {\n        return conversationQueries.getById.get(id);\n    },\n    // 更新对话标题\n    updateTitle (id, title) {\n        conversationQueries.updateTitle.run(title, id);\n    },\n    // 更新对话时间戳\n    updateTimestamp (id) {\n        conversationQueries.updateTimestamp.run(id);\n    },\n    // 删除对话\n    delete (id) {\n        conversationQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/conversations.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/index.ts":
/*!***********************************!*\
  !*** ./src/lib/database/index.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationOperations: () => (/* reexport safe */ _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations),\n/* harmony export */   conversationQueries: () => (/* reexport safe */ _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationQueries),\n/* harmony export */   db: () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   dbOperations: () => (/* binding */ dbOperations),\n/* harmony export */   \"default\": () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   initializeDatabase: () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase),\n/* harmony export */   mcpDbOperations: () => (/* binding */ mcpDbOperations),\n/* harmony export */   mcpServerOperations: () => (/* reexport safe */ _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* reexport safe */ _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerQueries),\n/* harmony export */   mcpToolCallOperations: () => (/* reexport safe */ _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations),\n/* harmony export */   mcpToolCallQueries: () => (/* reexport safe */ _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallQueries),\n/* harmony export */   mcpToolOperations: () => (/* reexport safe */ _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* reexport safe */ _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolQueries),\n/* harmony export */   messageOperations: () => (/* reexport safe */ _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations),\n/* harmony export */   messageQueries: () => (/* reexport safe */ _messages__WEBPACK_IMPORTED_MODULE_3__.messageQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/lib/database/types.ts\");\n/* harmony import */ var _conversations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conversations */ \"(rsc)/./src/lib/database/conversations.ts\");\n/* harmony import */ var _messages__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./messages */ \"(rsc)/./src/lib/database/messages.ts\");\n/* harmony import */ var _mcp_servers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mcp-servers */ \"(rsc)/./src/lib/database/mcp-servers.ts\");\n/* harmony import */ var _mcp_tools__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mcp-tools */ \"(rsc)/./src/lib/database/mcp-tools.ts\");\n/* harmony import */ var _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./mcp-tool-calls */ \"(rsc)/./src/lib/database/mcp-tool-calls.ts\");\n// 导出数据库连接\n\n// 导出所有类型定义\n\n// 导出各模块的操作函数\n\n\n\n\n\n// 为了保持向后兼容性，重新导出原有的操作对象\n\n\n\n\n\n// 兼容原有的 dbOperations 对象\nconst dbOperations = {\n    // 对话相关操作\n    createConversation: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.create,\n    getAllConversations: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.getAll,\n    getConversationById: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.getById,\n    updateConversationTitle: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.updateTitle,\n    updateConversationTimestamp: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.updateTimestamp,\n    deleteConversation: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.delete,\n    // 消息相关操作\n    createMessage: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.create,\n    getMessagesByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.getByConversationId,\n    deleteMessagesByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.deleteByConversationId,\n    getLastModelByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.getLastModelByConversationId,\n    // MCP工具调用相关操作\n    getMcpToolCallsByConversationId: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.getByConversationId\n};\n// 兼容原有的 mcpDbOperations 对象\nconst mcpDbOperations = {\n    // MCP服务器相关操作\n    createMcpServer: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.create,\n    getAllMcpServers: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getAll,\n    getMcpServerById: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getById,\n    getMcpServerByName: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getByName,\n    getEnabledMcpServers: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getEnabled,\n    updateMcpServerStatus: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.updateStatus,\n    deleteMcpServer: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.delete,\n    // MCP工具相关操作\n    createMcpTool: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.create,\n    getMcpToolsByServerId: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getByServerId,\n    getMcpToolById: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getById,\n    getMcpToolByServerIdAndName: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getByServerIdAndName,\n    getAvailableMcpTools: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getAvailable,\n    updateMcpToolUsage: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateUsage,\n    updateMcpToolAvailability: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateAvailability,\n    updateMcpToolEnabled: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateEnabled,\n    deleteMcpToolsByServerId: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.deleteByServerId,\n    deleteMcpTool: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.delete,\n    // MCP工具调用相关操作\n    createMcpToolCall: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.create,\n    getMcpToolCallsByConversationId: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.getByConversationId,\n    getMcpToolCallsByToolId: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.getByToolId,\n    getRecentMcpToolCalls: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.getRecent,\n    getMcpToolCallStats: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.getStats,\n    deleteMcpToolCallsByConversationId: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.deleteByConversationId,\n    deleteMcpToolCallsByToolId: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.deleteByToolId\n};\n// 默认导出数据库连接（保持兼容性）\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/mcp-servers.ts":
/*!*****************************************!*\
  !*** ./src/lib/database/mcp-servers.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mcpServerOperations: () => (/* binding */ mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* binding */ mcpServerQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// MCP服务器相关查询语句\nconst mcpServerQueries = {\n    // 创建MCP服务器\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO mcp_servers (\n      name, display_name, description, type, enabled,\n      command, args, working_directory,\n      url, base_url, port, path, protocol,\n      headers, auth_type, auth_config, timeout_ms, retry_attempts, retry_delay_ms,\n      extra_config\n    )\n    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n  `),\n    // 获取所有MCP服务器\n    getAll: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    ORDER BY created_at DESC\n  `),\n    // 根据ID获取MCP服务器\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE id = ?\n  `),\n    // 根据名称获取MCP服务器\n    getByName: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE name = ?\n  `),\n    // 获取启用的MCP服务器\n    getEnabled: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE enabled = 1\n    ORDER BY created_at DESC\n  `),\n    // 更新MCP服务器状态\n    updateStatus: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_servers\n    SET status = ?, error_message = ?, updated_at = CURRENT_TIMESTAMP,\n        last_connected_at = CASE WHEN ? = 'connected' THEN CURRENT_TIMESTAMP ELSE last_connected_at END\n    WHERE id = ?\n  `),\n    // 更新MCP服务器配置\n    update: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_servers\n    SET display_name = ?, description = ?, type = ?, enabled = ?,\n        command = ?, args = ?, working_directory = ?,\n        url = ?, base_url = ?, port = ?, path = ?, protocol = ?,\n        headers = ?, auth_type = ?, auth_config = ?, timeout_ms = ?, retry_attempts = ?, retry_delay_ms = ?,\n        extra_config = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除MCP服务器\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_servers\n    WHERE id = ?\n  `)\n};\n// MCP服务器数据库操作函数\nconst mcpServerOperations = {\n    // 创建MCP服务器\n    create (data) {\n        const result = mcpServerQueries.create.run(data.name, data.display_name, data.description || null, data.type, Boolean(data.enabled ?? true) ? 1 : 0, data.command || null, data.args ? JSON.stringify(data.args) : null, data.working_directory || null, data.url || null, data.base_url || null, data.port ? Number(data.port) : null, data.path || null, data.protocol || null, data.headers ? JSON.stringify(data.headers) : null, data.auth_type || null, data.auth_config ? JSON.stringify(data.auth_config) : null, data.timeout_ms ? Number(data.timeout_ms) : null, data.retry_attempts ? Number(data.retry_attempts) : null, data.retry_delay_ms ? Number(data.retry_delay_ms) : null, data.extra_config ? JSON.stringify(data.extra_config) : null);\n        return result.lastInsertRowid;\n    },\n    // 获取所有MCP服务器\n    getAll () {\n        return mcpServerQueries.getAll.all();\n    },\n    // 根据ID获取MCP服务器\n    getById (id) {\n        return mcpServerQueries.getById.get(id);\n    },\n    // 根据名称获取MCP服务器\n    getByName (name) {\n        return mcpServerQueries.getByName.get(name);\n    },\n    // 获取启用的MCP服务器\n    getEnabled () {\n        return mcpServerQueries.getEnabled.all();\n    },\n    // 更新MCP服务器状态\n    updateStatus (id, status, errorMessage) {\n        mcpServerQueries.updateStatus.run(status, errorMessage || null, status, id);\n    },\n    // 删除MCP服务器\n    delete (id) {\n        mcpServerQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/mcp-servers.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/mcp-tool-calls.ts":
/*!********************************************!*\
  !*** ./src/lib/database/mcp-tool-calls.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mcpToolCallOperations: () => (/* binding */ mcpToolCallOperations),\n/* harmony export */   mcpToolCallQueries: () => (/* binding */ mcpToolCallQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _mcp_tools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mcp-tools */ \"(rsc)/./src/lib/database/mcp-tools.ts\");\n\n\n// MCP工具调用相关查询语句\nconst mcpToolCallQueries = {\n    // 创建工具调用记录\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO mcp_tool_calls (\n      tool_id, conversation_id, message_id, input_args, output_result,\n      execution_time_ms, status, error_message\n    )\n    VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n  `),\n    // 获取对话的工具调用记录\n    getByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT tc.*, t.name as tool_name, s.name as server_name\n    FROM mcp_tool_calls tc\n    JOIN mcp_tools t ON tc.tool_id = t.id\n    JOIN mcp_servers s ON t.server_id = s.id\n    WHERE tc.conversation_id = ?\n    ORDER BY tc.created_at ASC\n  `),\n    // 获取工具的调用记录\n    getByToolId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tool_calls\n    WHERE tool_id = ?\n    ORDER BY created_at DESC\n  `),\n    // 获取最近的工具调用记录\n    getRecent: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT tc.*, t.name as tool_name, s.name as server_name\n    FROM mcp_tool_calls tc\n    JOIN mcp_tools t ON tc.tool_id = t.id\n    JOIN mcp_servers s ON t.server_id = s.id\n    ORDER BY tc.created_at DESC\n    LIMIT ?\n  `),\n    // 获取工具调用统计信息\n    getStats: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT \n      COUNT(*) as total_calls,\n      COUNT(CASE WHEN status = 'success' THEN 1 END) as success_calls,\n      COUNT(CASE WHEN status = 'error' THEN 1 END) as error_calls,\n      COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout_calls,\n      AVG(execution_time_ms) as avg_execution_time\n    FROM mcp_tool_calls\n    WHERE tool_id = ?\n  `),\n    // 删除对话的工具调用记录\n    deleteByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tool_calls\n    WHERE conversation_id = ?\n  `),\n    // 删除工具的调用记录\n    deleteByToolId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tool_calls\n    WHERE tool_id = ?\n  `)\n};\n// MCP工具调用数据库操作函数\nconst mcpToolCallOperations = {\n    // 创建工具调用记录\n    create (data) {\n        const result = mcpToolCallQueries.create.run(data.tool_id, data.conversation_id, data.message_id || null, data.input_args ? JSON.stringify(data.input_args) : null, data.output_result ? JSON.stringify(data.output_result) : null, data.execution_time_ms || null, data.status, data.error_message || null);\n        // 更新工具使用统计\n        if (data.status === 'success') {\n            _mcp_tools__WEBPACK_IMPORTED_MODULE_1__.mcpToolOperations.updateUsage(data.tool_id);\n        }\n        return result.lastInsertRowid;\n    },\n    // 获取对话的工具调用记录\n    getByConversationId (conversationId) {\n        return mcpToolCallQueries.getByConversationId.all(conversationId);\n    },\n    // 获取工具的调用记录\n    getByToolId (toolId) {\n        return mcpToolCallQueries.getByToolId.all(toolId);\n    },\n    // 获取最近的工具调用记录\n    getRecent (limit = 50) {\n        return mcpToolCallQueries.getRecent.all(limit);\n    },\n    // 获取工具调用统计信息\n    getStats (toolId) {\n        return mcpToolCallQueries.getStats.get(toolId);\n    },\n    // 删除对话的工具调用记录\n    deleteByConversationId (conversationId) {\n        mcpToolCallQueries.deleteByConversationId.run(conversationId);\n    },\n    // 删除工具的调用记录\n    deleteByToolId (toolId) {\n        mcpToolCallQueries.deleteByToolId.run(toolId);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/mcp-tool-calls.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/mcp-tools.ts":
/*!***************************************!*\
  !*** ./src/lib/database/mcp-tools.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mcpToolOperations: () => (/* binding */ mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* binding */ mcpToolQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// MCP工具相关查询语句\nconst mcpToolQueries = {\n    // 创建MCP工具\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO mcp_tools (server_id, name, description, input_schema, is_available, enabled)\n    VALUES (?, ?, ?, ?, ?, ?)\n  `),\n    // 获取服务器的所有工具\n    getByServerId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE server_id = ?\n    ORDER BY name ASC\n  `),\n    // 根据ID获取工具\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE id = ?\n  `),\n    // 根据服务器ID和工具名称获取工具\n    getByServerIdAndName: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE server_id = ? AND name = ?\n  `),\n    // 获取可用的工具\n    getAvailable: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT t.*, s.name as server_name, s.status as server_status\n    FROM mcp_tools t\n    JOIN mcp_servers s ON t.server_id = s.id\n    WHERE t.is_available = 1 AND t.enabled = 1 AND s.enabled = 1 AND s.status = 'connected'\n    ORDER BY t.name ASC\n  `),\n    // 更新工具使用统计\n    updateUsage: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET usage_count = usage_count + 1, last_used_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新工具可用性\n    updateAvailability: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET is_available = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新工具启用状态\n    updateEnabled: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET enabled = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除服务器的所有工具\n    deleteByServerId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tools\n    WHERE server_id = ?\n  `),\n    // 删除工具\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tools\n    WHERE id = ?\n  `)\n};\n// MCP工具数据库操作函数\nconst mcpToolOperations = {\n    // 创建MCP工具\n    create (data) {\n        const result = mcpToolQueries.create.run(data.server_id, data.name, data.description || null, data.input_schema ? JSON.stringify(data.input_schema) : null, Boolean(data.is_available ?? true) ? 1 : 0, Boolean(data.enabled ?? true) ? 1 : 0 // 确保布尔值转换为数字\n        );\n        return result.lastInsertRowid;\n    },\n    // 获取服务器的所有工具\n    getByServerId (serverId) {\n        return mcpToolQueries.getByServerId.all(serverId);\n    },\n    // 根据ID获取工具\n    getById (id) {\n        return mcpToolQueries.getById.get(id);\n    },\n    // 根据服务器ID和工具名称获取工具\n    getByServerIdAndName (serverId, name) {\n        return mcpToolQueries.getByServerIdAndName.get(serverId, name);\n    },\n    // 获取可用的工具\n    getAvailable () {\n        return mcpToolQueries.getAvailable.all();\n    },\n    // 更新工具使用统计\n    updateUsage (toolId) {\n        mcpToolQueries.updateUsage.run(toolId);\n    },\n    // 更新工具可用性\n    updateAvailability (id, isAvailable) {\n        mcpToolQueries.updateAvailability.run(isAvailable ? 1 : 0, id);\n    },\n    // 更新工具启用状态\n    updateEnabled (id, enabled) {\n        mcpToolQueries.updateEnabled.run(enabled ? 1 : 0, id);\n    },\n    // 删除服务器的所有工具\n    deleteByServerId (serverId) {\n        mcpToolQueries.deleteByServerId.run(serverId);\n    },\n    // 删除工具\n    delete (id) {\n        mcpToolQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/mcp-tools.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/messages.ts":
/*!**************************************!*\
  !*** ./src/lib/database/messages.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   messageOperations: () => (/* binding */ messageOperations),\n/* harmony export */   messageQueries: () => (/* binding */ messageQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _conversations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./conversations */ \"(rsc)/./src/lib/database/conversations.ts\");\n\n\n// 消息相关查询语句\nconst messageQueries = {\n    // 创建新消息\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO messages (\n      conversation_id, role, content, model, sequence_number, timestamp,\n      total_duration, load_duration, prompt_eval_count, prompt_eval_duration,\n      eval_count, eval_duration\n    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n  `),\n    // 获取对话的所有消息\n    getByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM messages\n    WHERE conversation_id = ?\n    ORDER BY timestamp ASC, sequence_number ASC\n  `),\n    // 删除对话的所有消息\n    deleteByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM messages\n    WHERE conversation_id = ?\n  `),\n    // 获取对话中最后使用的模型\n    getLastModelByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT model FROM messages\n    WHERE conversation_id = ? AND model IS NOT NULL\n    ORDER BY created_at DESC\n    LIMIT 1\n  `),\n    // 获取对话中下一个可用的序列号\n    getNextSequenceNumber: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT COALESCE(MAX(sequence_number), 0) + 1 as next_sequence\n    FROM messages\n    WHERE conversation_id = ?\n  `)\n};\n// 消息数据库操作函数\nconst messageOperations = {\n    // 创建新消息\n    create (data) {\n        // 自动获取下一个序列号\n        const sequenceNumber = data.sequence_number || messageOperations.getNextSequenceNumber(data.conversation_id);\n        // 生成时间戳（毫秒级）\n        const timestamp = Date.now();\n        const result = messageQueries.create.run(data.conversation_id, data.role, data.content, data.model || null, sequenceNumber, timestamp, data.total_duration || null, data.load_duration || null, data.prompt_eval_count || null, data.prompt_eval_duration || null, data.eval_count || null, data.eval_duration || null);\n        // 更新对话的时间戳\n        _conversations__WEBPACK_IMPORTED_MODULE_1__.conversationOperations.updateTimestamp(data.conversation_id);\n        return result.lastInsertRowid;\n    },\n    // 获取对话的所有消息\n    getByConversationId (conversationId) {\n        return messageQueries.getByConversationId.all(conversationId);\n    },\n    // 删除对话的所有消息\n    deleteByConversationId (conversationId) {\n        messageQueries.deleteByConversationId.run(conversationId);\n    },\n    // 获取对话中最后使用的模型\n    getLastModelByConversationId (conversationId) {\n        const result = messageQueries.getLastModelByConversationId.get(conversationId);\n        return result?.model || null;\n    },\n    // 获取对话中下一个可用的序列号\n    getNextSequenceNumber (conversationId) {\n        const result = messageQueries.getNextSequenceNumber.get(conversationId);\n        return result?.next_sequence || 1;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/messages.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/migrate-timestamps.ts":
/*!************************************************!*\
  !*** ./src/lib/database/migrate-timestamps.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autoMigrateIfNeeded: () => (/* binding */ autoMigrateIfNeeded),\n/* harmony export */   migrateMessageTimestamps: () => (/* binding */ migrateMessageTimestamps),\n/* harmony export */   needsTimestampMigration: () => (/* binding */ needsTimestampMigration)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n/**\n * 数据库迁移脚本：为现有消息添加timestamp字段\n * 这个脚本会为所有没有timestamp的消息生成基于created_at的时间戳\n */ function migrateMessageTimestamps() {\n    console.log('开始迁移消息时间戳...');\n    try {\n        // 开始事务\n        _connection__WEBPACK_IMPORTED_MODULE_0__.db.exec('BEGIN TRANSACTION');\n        // 查询所有没有timestamp或timestamp为null的消息\n        const messagesWithoutTimestamp = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n      SELECT id, created_at, sequence_number, conversation_id\n      FROM messages \n      WHERE timestamp IS NULL \n      ORDER BY conversation_id, created_at ASC, sequence_number ASC\n    `).all();\n        console.log(`找到 ${messagesWithoutTimestamp.length} 条需要迁移的消息`);\n        if (messagesWithoutTimestamp.length === 0) {\n            console.log('没有需要迁移的消息');\n            _connection__WEBPACK_IMPORTED_MODULE_0__.db.exec('COMMIT');\n            return;\n        }\n        // 准备更新语句\n        const updateTimestamp = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n      UPDATE messages \n      SET timestamp = ? \n      WHERE id = ?\n    `);\n        // 按对话分组处理，确保同一对话内的消息时间戳递增\n        const conversationGroups = new Map();\n        messagesWithoutTimestamp.forEach((message)=>{\n            if (!conversationGroups.has(message.conversation_id)) {\n                conversationGroups.set(message.conversation_id, []);\n            }\n            conversationGroups.get(message.conversation_id).push(message);\n        });\n        let updatedCount = 0;\n        // 为每个对话的消息生成递增的时间戳\n        conversationGroups.forEach((messages, conversationId)=>{\n            console.log(`处理对话 ${conversationId} 的 ${messages.length} 条消息`);\n            messages.forEach((message, index)=>{\n                // 基于created_at生成时间戳，并加上索引确保递增\n                const baseTimestamp = new Date(message.created_at).getTime();\n                const timestamp = baseTimestamp + index * 1000; // 每条消息间隔1秒\n                updateTimestamp.run(timestamp, message.id);\n                updatedCount++;\n            });\n        });\n        // 提交事务\n        _connection__WEBPACK_IMPORTED_MODULE_0__.db.exec('COMMIT');\n        console.log(`成功迁移 ${updatedCount} 条消息的时间戳`);\n        // 验证迁移结果\n        const remainingCount = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n      SELECT COUNT(*) as count \n      FROM messages \n      WHERE timestamp IS NULL\n    `).get();\n        if (remainingCount.count === 0) {\n            console.log('✅ 时间戳迁移完成，所有消息都有有效的timestamp');\n        } else {\n            console.warn(`⚠️ 仍有 ${remainingCount.count} 条消息没有timestamp`);\n        }\n    } catch (error) {\n        // 回滚事务\n        _connection__WEBPACK_IMPORTED_MODULE_0__.db.exec('ROLLBACK');\n        console.error('迁移失败，已回滚:', error);\n        throw error;\n    }\n}\n/**\n * 检查是否需要运行迁移\n */ function needsTimestampMigration() {\n    try {\n        const result = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n      SELECT COUNT(*) as count \n      FROM messages \n      WHERE timestamp IS NULL\n    `).get();\n        return result.count > 0;\n    } catch (error) {\n        console.error('检查迁移需求失败:', error);\n        return false;\n    }\n}\n/**\n * 自动运行迁移（如果需要）\n */ function autoMigrateIfNeeded() {\n    if (needsTimestampMigration()) {\n        console.log('检测到需要迁移时间戳，开始自动迁移...');\n        migrateMessageTimestamps();\n    } else {\n        console.log('所有消息都有有效的timestamp，无需迁移');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/migrate-timestamps.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/types.ts":
/*!***********************************!*\
  !*** ./src/lib/database/types.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// 对话相关接口\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/types.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp/mcp-client-server.ts":
/*!******************************************!*\
  !*** ./src/lib/mcp/mcp-client-server.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   McpServerClient: () => (/* binding */ McpServerClient),\n/* harmony export */   mcpServerClient: () => (/* binding */ mcpServerClient)\n/* harmony export */ });\n/* harmony import */ var _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/index.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js\");\n/* harmony import */ var _modelcontextprotocol_sdk_client_stdio_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/stdio.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/stdio.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * 服务器端MCP客户端实现\n * 只能在Node.js环境中使用，用于API路由\n */ \n\n\n/**\n * 服务器端MCP客户端类\n * 负责与MCP服务器的通信\n */ class McpServerClient {\n    /**\n   * 连接到MCP服务器\n   */ async connect() {\n        try {\n            // 创建stdio传输，直接启动MCP服务器\n            const serverPath = path__WEBPACK_IMPORTED_MODULE_2__.join(process.cwd(), 'src', 'lib', 'mcp', 'mcp-server.ts');\n            this.transport = new _modelcontextprotocol_sdk_client_stdio_js__WEBPACK_IMPORTED_MODULE_1__.StdioClientTransport({\n                command: 'npx',\n                args: [\n                    'tsx',\n                    serverPath\n                ]\n            });\n            // 创建客户端\n            this.client = new _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__.Client({\n                name: 'kun-agent-server-client',\n                version: '1.0.0'\n            }, {\n                capabilities: {\n                    tools: {}\n                }\n            });\n            // 连接到服务器\n            await this.client.connect(this.transport);\n            this.isConnected = true;\n            // 获取可用工具\n            await this.refreshTools();\n            console.log('服务器端MCP客户端连接成功');\n            return true;\n        } catch (error) {\n            console.error('服务器端MCP客户端连接失败:', error);\n            await this.disconnect();\n            return false;\n        }\n    }\n    /**\n   * 断开连接\n   */ async disconnect() {\n        try {\n            if (this.client) {\n                await this.client.close();\n                this.client = null;\n            }\n            if (this.transport) {\n                await this.transport.close();\n                this.transport = null;\n            }\n            this.isConnected = false;\n            this.availableTools = [];\n            console.log('服务器端MCP客户端已断开连接');\n        } catch (error) {\n            console.error('断开MCP连接时出错:', error);\n        }\n    }\n    /**\n   * 检查是否已连接\n   */ isClientConnected() {\n        return this.isConnected && this.client !== null;\n    }\n    /**\n   * 刷新工具列表\n   */ async refreshTools() {\n        if (!this.client || !this.isConnected) {\n            return [];\n        }\n        try {\n            const response = await this.client.listTools();\n            this.availableTools = response.tools.map((tool)=>{\n                // 清理inputSchema，移除$schema字段以符合Ollama要求\n                const cleanInputSchema = tool.inputSchema ? {\n                    ...tool.inputSchema\n                } : {};\n                if (cleanInputSchema && typeof cleanInputSchema === 'object' && '$schema' in cleanInputSchema) {\n                    delete cleanInputSchema.$schema;\n                }\n                return {\n                    name: tool.name,\n                    description: tool.description || '',\n                    inputSchema: cleanInputSchema\n                };\n            });\n            console.log(`已获取 ${this.availableTools.length} 个MCP工具`);\n            return this.availableTools;\n        } catch (error) {\n            console.error('获取MCP工具列表失败:', error);\n            return [];\n        }\n    }\n    /**\n   * 获取可用工具列表\n   */ getAvailableTools() {\n        return this.availableTools;\n    }\n    /**\n   * 检查工具是否可用\n   */ isToolAvailable(toolName) {\n        return this.availableTools.some((tool)=>tool.name === toolName);\n    }\n    /**\n   * 调用工具\n   */ async callTool(toolCall) {\n        if (!this.client || !this.isConnected) {\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: 'MCP客户端未连接'\n                    }\n                ],\n                isError: true\n            };\n        }\n        try {\n            const result = await this.client.callTool({\n                name: toolCall.name,\n                arguments: toolCall.arguments\n            });\n            // 根据最新的MCP SDK，result.content 已经是正确的格式\n            const content = result.content || [\n                {\n                    type: 'text',\n                    text: '工具执行成功，但没有返回内容'\n                }\n            ];\n            return {\n                content: content.map((item)=>({\n                        type: 'text',\n                        text: item.type === 'text' ? item.text : JSON.stringify(item)\n                    })),\n                isError: false\n            };\n        } catch (error) {\n            console.error('工具调用失败:', error);\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: `工具调用失败: ${error instanceof Error ? error.message : '未知错误'}`\n                    }\n                ],\n                isError: true\n            };\n        }\n    }\n    constructor(){\n        this.client = null;\n        this.transport = null;\n        this.isConnected = false;\n        this.availableTools = [];\n    }\n}\n// 导出类和单例实例\n\nconst mcpServerClient = new McpServerClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL21jcC9tY3AtY2xpZW50LXNlcnZlci50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTs7O0NBR0MsR0FFa0U7QUFDYztBQUNwRDtBQXdCN0I7OztDQUdDLEdBQ0QsTUFBTUc7SUFNSjs7R0FFQyxHQUNELE1BQU1DLFVBQTRCO1FBQ2hDLElBQUk7WUFDRix1QkFBdUI7WUFDdkIsTUFBTUMsYUFBYUgsc0NBQVMsQ0FBQ0ssUUFBUUMsR0FBRyxJQUFJLE9BQU8sT0FBTyxPQUFPO1lBRWpFLElBQUksQ0FBQ0MsU0FBUyxHQUFHLElBQUlSLDJGQUFvQkEsQ0FBQztnQkFDeENTLFNBQVM7Z0JBQ1RDLE1BQU07b0JBQUM7b0JBQU9OO2lCQUFXO1lBQzNCO1lBRUEsUUFBUTtZQUNSLElBQUksQ0FBQ08sTUFBTSxHQUFHLElBQUlaLDZFQUFNQSxDQUFDO2dCQUN2QmEsTUFBTTtnQkFDTkMsU0FBUztZQUNYLEdBQUc7Z0JBQ0RDLGNBQWM7b0JBQ1pDLE9BQU8sQ0FBQztnQkFDVjtZQUNGO1lBRUEsU0FBUztZQUNULE1BQU0sSUFBSSxDQUFDSixNQUFNLENBQUNSLE9BQU8sQ0FBQyxJQUFJLENBQUNLLFNBQVM7WUFDeEMsSUFBSSxDQUFDUSxXQUFXLEdBQUc7WUFFbkIsU0FBUztZQUNULE1BQU0sSUFBSSxDQUFDQyxZQUFZO1lBRXZCQyxRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPO1FBQ1QsRUFBRSxPQUFPQyxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyxtQkFBbUJBO1lBQ2pDLE1BQU0sSUFBSSxDQUFDQyxVQUFVO1lBQ3JCLE9BQU87UUFDVDtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNQSxhQUE0QjtRQUNoQyxJQUFJO1lBQ0YsSUFBSSxJQUFJLENBQUNWLE1BQU0sRUFBRTtnQkFDZixNQUFNLElBQUksQ0FBQ0EsTUFBTSxDQUFDVyxLQUFLO2dCQUN2QixJQUFJLENBQUNYLE1BQU0sR0FBRztZQUNoQjtZQUVBLElBQUksSUFBSSxDQUFDSCxTQUFTLEVBQUU7Z0JBQ2xCLE1BQU0sSUFBSSxDQUFDQSxTQUFTLENBQUNjLEtBQUs7Z0JBQzFCLElBQUksQ0FBQ2QsU0FBUyxHQUFHO1lBQ25CO1lBRUEsSUFBSSxDQUFDUSxXQUFXLEdBQUc7WUFDbkIsSUFBSSxDQUFDTyxjQUFjLEdBQUcsRUFBRTtZQUN4QkwsUUFBUUMsR0FBRyxDQUFDO1FBQ2QsRUFBRSxPQUFPQyxPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyxlQUFlQTtRQUMvQjtJQUNGO0lBRUE7O0dBRUMsR0FDREksb0JBQTZCO1FBQzNCLE9BQU8sSUFBSSxDQUFDUixXQUFXLElBQUksSUFBSSxDQUFDTCxNQUFNLEtBQUs7SUFDN0M7SUFFQTs7R0FFQyxHQUNELE1BQU1NLGVBQW1DO1FBQ3ZDLElBQUksQ0FBQyxJQUFJLENBQUNOLE1BQU0sSUFBSSxDQUFDLElBQUksQ0FBQ0ssV0FBVyxFQUFFO1lBQ3JDLE9BQU8sRUFBRTtRQUNYO1FBRUEsSUFBSTtZQUNGLE1BQU1TLFdBQVcsTUFBTSxJQUFJLENBQUNkLE1BQU0sQ0FBQ2UsU0FBUztZQUM1QyxJQUFJLENBQUNILGNBQWMsR0FBR0UsU0FBU1YsS0FBSyxDQUFDWSxHQUFHLENBQUNDLENBQUFBO2dCQUN0Qyx1Q0FBdUM7Z0JBQ3ZDLE1BQU1DLG1CQUF3QkQsS0FBS0UsV0FBVyxHQUFHO29CQUFFLEdBQUdGLEtBQUtFLFdBQVc7Z0JBQUMsSUFBSSxDQUFDO2dCQUM1RSxJQUFJRCxvQkFBb0IsT0FBT0EscUJBQXFCLFlBQVksYUFBYUEsa0JBQWtCO29CQUM3RixPQUFPQSxpQkFBaUJFLE9BQU87Z0JBQ2pDO2dCQUVBLE9BQU87b0JBQ0xuQixNQUFNZ0IsS0FBS2hCLElBQUk7b0JBQ2ZvQixhQUFhSixLQUFLSSxXQUFXLElBQUk7b0JBQ2pDRixhQUFhRDtnQkFDZjtZQUNGO1lBRURYLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLElBQUksRUFBRSxJQUFJLENBQUNJLGNBQWMsQ0FBQ1UsTUFBTSxDQUFDLE9BQU8sQ0FBQztZQUN0RCxPQUFPLElBQUksQ0FBQ1YsY0FBYztRQUM1QixFQUFFLE9BQU9ILE9BQU87WUFDZEYsUUFBUUUsS0FBSyxDQUFDLGdCQUFnQkE7WUFDOUIsT0FBTyxFQUFFO1FBQ1g7SUFDRjtJQUVBOztHQUVDLEdBQ0RjLG9CQUErQjtRQUM3QixPQUFPLElBQUksQ0FBQ1gsY0FBYztJQUM1QjtJQUVBOztHQUVDLEdBQ0RZLGdCQUFnQkMsUUFBZ0IsRUFBVztRQUN6QyxPQUFPLElBQUksQ0FBQ2IsY0FBYyxDQUFDYyxJQUFJLENBQUNULENBQUFBLE9BQVFBLEtBQUtoQixJQUFJLEtBQUt3QjtJQUN4RDtJQUVBOztHQUVDLEdBQ0QsTUFBTUUsU0FBU0MsUUFBcUIsRUFBMEI7UUFDNUQsSUFBSSxDQUFDLElBQUksQ0FBQzVCLE1BQU0sSUFBSSxDQUFDLElBQUksQ0FBQ0ssV0FBVyxFQUFFO1lBQ3JDLE9BQU87Z0JBQ0x3QixTQUFTO29CQUFDO3dCQUNSQyxNQUFNO3dCQUNOQyxNQUFNO29CQUNSO2lCQUFFO2dCQUNGQyxTQUFTO1lBQ1g7UUFDRjtRQUVBLElBQUk7WUFDRixNQUFNQyxTQUFTLE1BQU0sSUFBSSxDQUFDakMsTUFBTSxDQUFDMkIsUUFBUSxDQUFDO2dCQUN4QzFCLE1BQU0yQixTQUFTM0IsSUFBSTtnQkFDbkJpQyxXQUFXTixTQUFTTSxTQUFTO1lBQy9CO1lBRUEsdUNBQXVDO1lBQ3ZDLE1BQU1MLFVBQVUsT0FBZ0JBLE9BQU8sSUFBSTtnQkFBQztvQkFDMUNDLE1BQU07b0JBQ05DLE1BQU07Z0JBQ1I7YUFBRTtZQUVGLE9BQU87Z0JBQ0xGLFNBQVNBLFFBQVFiLEdBQUcsQ0FBQyxDQUFDbUIsT0FBZTt3QkFDbkNMLE1BQU07d0JBQ05DLE1BQU1JLEtBQUtMLElBQUksS0FBSyxTQUFTSyxLQUFLSixJQUFJLEdBQUdLLEtBQUtDLFNBQVMsQ0FBQ0Y7b0JBQzFEO2dCQUNBSCxTQUFTO1lBQ1g7UUFDRixFQUFFLE9BQU92QixPQUFPO1lBQ2RGLFFBQVFFLEtBQUssQ0FBQyxXQUFXQTtZQUN6QixPQUFPO2dCQUNMb0IsU0FBUztvQkFBQzt3QkFDUkMsTUFBTTt3QkFDTkMsTUFBTSxDQUFDLFFBQVEsRUFBRXRCLGlCQUFpQjZCLFFBQVE3QixNQUFNOEIsT0FBTyxHQUFHLFFBQVE7b0JBQ3BFO2lCQUFFO2dCQUNGUCxTQUFTO1lBQ1g7UUFDRjtJQUNGOzthQW5LUWhDLFNBQXdCO2FBQ3hCSCxZQUF5QzthQUN6Q1EsY0FBYzthQUNkTyxpQkFBNEIsRUFBRTs7QUFpS3hDO0FBRUEsV0FBVztBQUNnQjtBQUNwQixNQUFNNEIsa0JBQWtCLElBQUlqRCxrQkFBa0IiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcWmFja1xcRGVza3RvcFxcUlAzMF9rdW5hZ2VudFxcZnJvbnRlbmRcXHNyY1xcbGliXFxtY3BcXG1jcC1jbGllbnQtc2VydmVyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICog5pyN5Yqh5Zmo56uvTUNQ5a6i5oi356uv5a6e546wXG4gKiDlj6rog73lnKhOb2RlLmpz546v5aKD5Lit5L2/55So77yM55So5LqOQVBJ6Lev55SxXG4gKi9cblxuaW1wb3J0IHsgQ2xpZW50IH0gZnJvbSAnQG1vZGVsY29udGV4dHByb3RvY29sL3Nkay9jbGllbnQvaW5kZXguanMnO1xuaW1wb3J0IHsgU3RkaW9DbGllbnRUcmFuc3BvcnQgfSBmcm9tICdAbW9kZWxjb250ZXh0cHJvdG9jb2wvc2RrL2NsaWVudC9zdGRpby5qcyc7XG5pbXBvcnQgKiBhcyBwYXRoIGZyb20gJ3BhdGgnO1xuXG4vLyBNQ1Dlt6XlhbfmjqXlj6NcbmV4cG9ydCBpbnRlcmZhY2UgTWNwVG9vbCB7XG4gIG5hbWU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgaW5wdXRTY2hlbWE6IGFueTtcbn1cblxuLy8gTUNQ5bel5YW36LCD55So5o6l5Y+jXG5leHBvcnQgaW50ZXJmYWNlIE1jcFRvb2xDYWxsIHtcbiAgbmFtZTogc3RyaW5nO1xuICBhcmd1bWVudHM6IFJlY29yZDxzdHJpbmcsIGFueT47XG59XG5cbi8vIE1DUOW3peWFt+e7k+aenOaOpeWPo1xuZXhwb3J0IGludGVyZmFjZSBNY3BUb29sUmVzdWx0IHtcbiAgY29udGVudDogQXJyYXk8e1xuICAgIHR5cGU6ICd0ZXh0JztcbiAgICB0ZXh0OiBzdHJpbmc7XG4gIH0+O1xuICBpc0Vycm9yPzogYm9vbGVhbjtcbn1cblxuLyoqXG4gKiDmnI3liqHlmajnq69NQ1DlrqLmiLfnq6/nsbtcbiAqIOi0n+i0o+S4jk1DUOacjeWKoeWZqOeahOmAmuS/oVxuICovXG5jbGFzcyBNY3BTZXJ2ZXJDbGllbnQge1xuICBwcml2YXRlIGNsaWVudDogQ2xpZW50IHwgbnVsbCA9IG51bGw7XG4gIHByaXZhdGUgdHJhbnNwb3J0OiBTdGRpb0NsaWVudFRyYW5zcG9ydCB8IG51bGwgPSBudWxsO1xuICBwcml2YXRlIGlzQ29ubmVjdGVkID0gZmFsc2U7XG4gIHByaXZhdGUgYXZhaWxhYmxlVG9vbHM6IE1jcFRvb2xbXSA9IFtdO1xuXG4gIC8qKlxuICAgKiDov57mjqXliLBNQ1DmnI3liqHlmahcbiAgICovXG4gIGFzeW5jIGNvbm5lY3QoKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIOWIm+W7unN0ZGlv5Lyg6L6T77yM55u05o6l5ZCv5YqoTUNQ5pyN5Yqh5ZmoXG4gICAgICBjb25zdCBzZXJ2ZXJQYXRoID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksICdzcmMnLCAnbGliJywgJ21jcCcsICdtY3Atc2VydmVyLnRzJyk7XG4gICAgICBcbiAgICAgIHRoaXMudHJhbnNwb3J0ID0gbmV3IFN0ZGlvQ2xpZW50VHJhbnNwb3J0KHtcbiAgICAgICAgY29tbWFuZDogJ25weCcsXG4gICAgICAgIGFyZ3M6IFsndHN4Jywgc2VydmVyUGF0aF1cbiAgICAgIH0pO1xuXG4gICAgICAvLyDliJvlu7rlrqLmiLfnq69cbiAgICAgIHRoaXMuY2xpZW50ID0gbmV3IENsaWVudCh7XG4gICAgICAgIG5hbWU6ICdrdW4tYWdlbnQtc2VydmVyLWNsaWVudCcsXG4gICAgICAgIHZlcnNpb246ICcxLjAuMCdcbiAgICAgIH0sIHtcbiAgICAgICAgY2FwYWJpbGl0aWVzOiB7XG4gICAgICAgICAgdG9vbHM6IHt9XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgICAvLyDov57mjqXliLDmnI3liqHlmahcbiAgICAgIGF3YWl0IHRoaXMuY2xpZW50LmNvbm5lY3QodGhpcy50cmFuc3BvcnQpO1xuICAgICAgdGhpcy5pc0Nvbm5lY3RlZCA9IHRydWU7XG5cbiAgICAgIC8vIOiOt+WPluWPr+eUqOW3peWFt1xuICAgICAgYXdhaXQgdGhpcy5yZWZyZXNoVG9vbHMoKTtcblxuICAgICAgY29uc29sZS5sb2coJ+acjeWKoeWZqOerr01DUOWuouaIt+err+i/nuaOpeaIkOWKnycpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+acjeWKoeWZqOerr01DUOWuouaIt+err+i/nuaOpeWksei0pTonLCBlcnJvcik7XG4gICAgICBhd2FpdCB0aGlzLmRpc2Nvbm5lY3QoKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICog5pat5byA6L+e5o6lXG4gICAqL1xuICBhc3luYyBkaXNjb25uZWN0KCk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAodGhpcy5jbGllbnQpIHtcbiAgICAgICAgYXdhaXQgdGhpcy5jbGllbnQuY2xvc2UoKTtcbiAgICAgICAgdGhpcy5jbGllbnQgPSBudWxsO1xuICAgICAgfVxuXG4gICAgICBpZiAodGhpcy50cmFuc3BvcnQpIHtcbiAgICAgICAgYXdhaXQgdGhpcy50cmFuc3BvcnQuY2xvc2UoKTtcbiAgICAgICAgdGhpcy50cmFuc3BvcnQgPSBudWxsO1xuICAgICAgfVxuXG4gICAgICB0aGlzLmlzQ29ubmVjdGVkID0gZmFsc2U7XG4gICAgICB0aGlzLmF2YWlsYWJsZVRvb2xzID0gW107XG4gICAgICBjb25zb2xlLmxvZygn5pyN5Yqh5Zmo56uvTUNQ5a6i5oi356uv5bey5pat5byA6L+e5o6lJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+aWreW8gE1DUOi/nuaOpeaXtuWHuumUmTonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIOajgOafpeaYr+WQpuW3sui/nuaOpVxuICAgKi9cbiAgaXNDbGllbnRDb25uZWN0ZWQoKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHRoaXMuaXNDb25uZWN0ZWQgJiYgdGhpcy5jbGllbnQgIT09IG51bGw7XG4gIH1cblxuICAvKipcbiAgICog5Yi35paw5bel5YW35YiX6KGoXG4gICAqL1xuICBhc3luYyByZWZyZXNoVG9vbHMoKTogUHJvbWlzZTxNY3BUb29sW10+IHtcbiAgICBpZiAoIXRoaXMuY2xpZW50IHx8ICF0aGlzLmlzQ29ubmVjdGVkKSB7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQubGlzdFRvb2xzKCk7XG4gICAgICB0aGlzLmF2YWlsYWJsZVRvb2xzID0gcmVzcG9uc2UudG9vbHMubWFwKHRvb2wgPT4ge1xuICAgICAgICAgLy8g5riF55CGaW5wdXRTY2hlbWHvvIznp7vpmaQkc2NoZW1h5a2X5q615Lul56ym5ZCIT2xsYW1h6KaB5rGCXG4gICAgICAgICBjb25zdCBjbGVhbklucHV0U2NoZW1hOiBhbnkgPSB0b29sLmlucHV0U2NoZW1hID8geyAuLi50b29sLmlucHV0U2NoZW1hIH0gOiB7fTtcbiAgICAgICAgIGlmIChjbGVhbklucHV0U2NoZW1hICYmIHR5cGVvZiBjbGVhbklucHV0U2NoZW1hID09PSAnb2JqZWN0JyAmJiAnJHNjaGVtYScgaW4gY2xlYW5JbnB1dFNjaGVtYSkge1xuICAgICAgICAgICBkZWxldGUgY2xlYW5JbnB1dFNjaGVtYS4kc2NoZW1hO1xuICAgICAgICAgfVxuICAgICAgICAgXG4gICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICBuYW1lOiB0b29sLm5hbWUsXG4gICAgICAgICAgIGRlc2NyaXB0aW9uOiB0b29sLmRlc2NyaXB0aW9uIHx8ICcnLFxuICAgICAgICAgICBpbnB1dFNjaGVtYTogY2xlYW5JbnB1dFNjaGVtYVxuICAgICAgICAgfTtcbiAgICAgICB9KTtcbiAgICAgIFxuICAgICAgY29uc29sZS5sb2coYOW3suiOt+WPliAke3RoaXMuYXZhaWxhYmxlVG9vbHMubGVuZ3RofSDkuKpNQ1Dlt6XlhbdgKTtcbiAgICAgIHJldHVybiB0aGlzLmF2YWlsYWJsZVRvb2xzO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfojrflj5ZNQ1Dlt6XlhbfliJfooajlpLHotKU6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIFtdO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiDojrflj5blj6/nlKjlt6XlhbfliJfooahcbiAgICovXG4gIGdldEF2YWlsYWJsZVRvb2xzKCk6IE1jcFRvb2xbXSB7XG4gICAgcmV0dXJuIHRoaXMuYXZhaWxhYmxlVG9vbHM7XG4gIH1cblxuICAvKipcbiAgICog5qOA5p+l5bel5YW35piv5ZCm5Y+v55SoXG4gICAqL1xuICBpc1Rvb2xBdmFpbGFibGUodG9vbE5hbWU6IHN0cmluZyk6IGJvb2xlYW4ge1xuICAgIHJldHVybiB0aGlzLmF2YWlsYWJsZVRvb2xzLnNvbWUodG9vbCA9PiB0b29sLm5hbWUgPT09IHRvb2xOYW1lKTtcbiAgfVxuXG4gIC8qKlxuICAgKiDosIPnlKjlt6XlhbdcbiAgICovXG4gIGFzeW5jIGNhbGxUb29sKHRvb2xDYWxsOiBNY3BUb29sQ2FsbCk6IFByb21pc2U8TWNwVG9vbFJlc3VsdD4ge1xuICAgIGlmICghdGhpcy5jbGllbnQgfHwgIXRoaXMuaXNDb25uZWN0ZWQpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGNvbnRlbnQ6IFt7XG4gICAgICAgICAgdHlwZTogJ3RleHQnIGFzIGNvbnN0LFxuICAgICAgICAgIHRleHQ6ICdNQ1DlrqLmiLfnq6/mnKrov57mjqUnXG4gICAgICAgIH1dLFxuICAgICAgICBpc0Vycm9yOiB0cnVlXG4gICAgICB9O1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLmNsaWVudC5jYWxsVG9vbCh7XG4gICAgICAgIG5hbWU6IHRvb2xDYWxsLm5hbWUsXG4gICAgICAgIGFyZ3VtZW50czogdG9vbENhbGwuYXJndW1lbnRzXG4gICAgICB9KTtcblxuICAgICAgLy8g5qC55o2u5pyA5paw55qETUNQIFNES++8jHJlc3VsdC5jb250ZW50IOW3sue7j+aYr+ato+ehrueahOagvOW8j1xuICAgICAgY29uc3QgY29udGVudCA9IChyZXN1bHQgYXMgYW55KS5jb250ZW50IHx8IFt7XG4gICAgICAgIHR5cGU6ICd0ZXh0JyBhcyBjb25zdCxcbiAgICAgICAgdGV4dDogJ+W3peWFt+aJp+ihjOaIkOWKn++8jOS9huayoeaciei/lOWbnuWGheWuuSdcbiAgICAgIH1dO1xuICAgICAgXG4gICAgICByZXR1cm4ge1xuICAgICAgICBjb250ZW50OiBjb250ZW50Lm1hcCgoaXRlbTogYW55KSA9PiAoe1xuICAgICAgICAgIHR5cGU6ICd0ZXh0JyBhcyBjb25zdCxcbiAgICAgICAgICB0ZXh0OiBpdGVtLnR5cGUgPT09ICd0ZXh0JyA/IGl0ZW0udGV4dCA6IEpTT04uc3RyaW5naWZ5KGl0ZW0pXG4gICAgICAgIH0pKSxcbiAgICAgICAgaXNFcnJvcjogZmFsc2VcbiAgICAgIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+W3peWFt+iwg+eUqOWksei0pTonLCBlcnJvcik7XG4gICAgICByZXR1cm4ge1xuICAgICAgICBjb250ZW50OiBbe1xuICAgICAgICAgIHR5cGU6ICd0ZXh0JyBhcyBjb25zdCxcbiAgICAgICAgICB0ZXh0OiBg5bel5YW36LCD55So5aSx6LSlOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ+acquefpemUmeivryd9YFxuICAgICAgICB9XSxcbiAgICAgICAgaXNFcnJvcjogdHJ1ZVxuICAgICAgfTtcbiAgICB9XG4gIH1cbn1cblxuLy8g5a+85Ye657G75ZKM5Y2V5L6L5a6e5L6LXG5leHBvcnQgeyBNY3BTZXJ2ZXJDbGllbnQgfTtcbmV4cG9ydCBjb25zdCBtY3BTZXJ2ZXJDbGllbnQgPSBuZXcgTWNwU2VydmVyQ2xpZW50KCk7Il0sIm5hbWVzIjpbIkNsaWVudCIsIlN0ZGlvQ2xpZW50VHJhbnNwb3J0IiwicGF0aCIsIk1jcFNlcnZlckNsaWVudCIsImNvbm5lY3QiLCJzZXJ2ZXJQYXRoIiwiam9pbiIsInByb2Nlc3MiLCJjd2QiLCJ0cmFuc3BvcnQiLCJjb21tYW5kIiwiYXJncyIsImNsaWVudCIsIm5hbWUiLCJ2ZXJzaW9uIiwiY2FwYWJpbGl0aWVzIiwidG9vbHMiLCJpc0Nvbm5lY3RlZCIsInJlZnJlc2hUb29scyIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsImRpc2Nvbm5lY3QiLCJjbG9zZSIsImF2YWlsYWJsZVRvb2xzIiwiaXNDbGllbnRDb25uZWN0ZWQiLCJyZXNwb25zZSIsImxpc3RUb29scyIsIm1hcCIsInRvb2wiLCJjbGVhbklucHV0U2NoZW1hIiwiaW5wdXRTY2hlbWEiLCIkc2NoZW1hIiwiZGVzY3JpcHRpb24iLCJsZW5ndGgiLCJnZXRBdmFpbGFibGVUb29scyIsImlzVG9vbEF2YWlsYWJsZSIsInRvb2xOYW1lIiwic29tZSIsImNhbGxUb29sIiwidG9vbENhbGwiLCJjb250ZW50IiwidHlwZSIsInRleHQiLCJpc0Vycm9yIiwicmVzdWx0IiwiYXJndW1lbnRzIiwiaXRlbSIsIkpTT04iLCJzdHJpbmdpZnkiLCJFcnJvciIsIm1lc3NhZ2UiLCJtY3BTZXJ2ZXJDbGllbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp/mcp-client-server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp/mcp-client-sse.ts":
/*!***************************************!*\
  !*** ./src/lib/mcp/mcp-client-sse.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/index.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js\");\n/* harmony import */ var _modelcontextprotocol_sdk_client_sse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/sse.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/sse.js\");\n/**\n * SSE MCP客户端实现\n * 支持通过HTTP Server-Sent Events连接到远程MCP服务器\n */ \n\n/**\n * SSE MCP客户端类\n * 负责与远程SSE MCP服务器的通信\n */ class SSEMcpClient {\n    constructor(config){\n        this.client = null;\n        this.transport = null;\n        this.isConnected = false;\n        this.availableTools = [];\n        this.config = config;\n    }\n    /**\n   * 连接到SSE MCP服务器\n   */ async connect() {\n        const maxRetries = this.config.retryAttempts || 3;\n        const baseDelay = 1000; // 1秒基础延迟\n        // 根据MCP协议规范配置请求头（移到循环外部以便在catch块中访问）\n        const headers = {\n            'Accept': 'text/event-stream',\n            'Cache-Control': 'no-cache',\n            'Connection': 'keep-alive',\n            'Content-Type': 'application/json',\n            'User-Agent': 'kun-agent-sse-client/1.0.0'\n        };\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                console.log(`正在连接到SSE MCP服务器: ${this.config.url} (尝试 ${attempt}/${maxRetries})`);\n                // 创建SSE传输，添加必要的请求头\n                const url = new URL(this.config.url);\n                // 重置headers为基础配置\n                Object.keys(headers).forEach((key)=>{\n                    if (![\n                        'Accept',\n                        'Cache-Control',\n                        'Connection',\n                        'Content-Type',\n                        'User-Agent'\n                    ].includes(key)) {\n                        delete headers[key];\n                    }\n                });\n                // 重新设置基础请求头\n                headers['Accept'] = 'text/event-stream';\n                headers['Cache-Control'] = 'no-cache';\n                headers['Connection'] = 'keep-alive';\n                headers['Content-Type'] = 'application/json';\n                headers['User-Agent'] = 'kun-agent-sse-client/1.0.0';\n                // 添加MCP协议要求的会话ID头（生成唯一会话ID）\n                const sessionId = `mcp-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n                headers['Mcp-Session-Id'] = sessionId;\n                // 添加MCP协议版本头\n                const protocolVersion = this.config.protocolVersion || '2025-03-26';\n                headers['Mcp-Protocol-Version'] = protocolVersion;\n                // 添加CORS相关头部\n                headers['Access-Control-Request-Method'] = 'GET';\n                headers['Access-Control-Request-Headers'] = 'Content-Type, Authorization, Mcp-Session-Id, Mcp-Protocol-Version';\n                // 如果配置中包含API密钥，添加认证头\n                if (this.config.apiKey) {\n                    headers['Authorization'] = `Bearer ${this.config.apiKey}`;\n                }\n                // 合并自定义请求头\n                if (this.config.headers) {\n                    Object.assign(headers, this.config.headers);\n                }\n                // 创建传输配置\n                const transportConfig = {\n                    requestInit: {\n                        headers\n                    }\n                };\n                // 添加超时配置\n                if (this.config.timeout) {\n                    transportConfig.requestInit.timeout = this.config.timeout;\n                }\n                this.transport = new _modelcontextprotocol_sdk_client_sse_js__WEBPACK_IMPORTED_MODULE_1__.SSEClientTransport(url, transportConfig);\n                // 创建客户端\n                this.client = new _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__.Client({\n                    name: `kun-agent-sse-client-${this.config.name}`,\n                    version: '1.0.0'\n                }, {\n                    capabilities: {\n                        tools: {}\n                    }\n                });\n                // 连接到服务器\n                await this.client.connect(this.transport);\n                this.isConnected = true;\n                // 获取可用工具\n                await this.refreshTools();\n                console.log(`SSE MCP客户端连接成功: ${this.config.name}`);\n                return true;\n            } catch (error) {\n                console.error(`SSE MCP客户端连接失败 (${this.config.name}) - 尝试 ${attempt}:`, error);\n                // 检查错误类型\n                const is429Error = error?.code === 429 || error?.event?.code === 429 || error?.message?.includes('429') || error?.message?.includes('Too Many Requests');\n                const is412Error = error?.code === 412 || error?.event?.code === 412 || error?.message?.includes('412') || error?.message?.includes('Precondition Failed');\n                if (is412Error) {\n                    console.error(`检测到412错误 (${this.config.name})，这通常表示前置条件失败:`);\n                    console.error('可能的原因:');\n                    console.error('1. 服务器要求特定的认证头或API密钥');\n                    console.error('2. 请求头不符合MCP SSE协议要求');\n                    console.error('3. 服务器CORS配置不允许当前域名访问');\n                    console.error('4. 服务器不支持当前的MCP协议版本 (2025-03-26)');\n                    console.error('5. 会话ID格式不正确或服务器不接受会话管理');\n                    console.error('');\n                    console.error('解决建议:');\n                    console.error('- 检查服务器是否需要API密钥认证');\n                    console.error('- 验证服务器CORS配置是否正确');\n                    console.error('- 确认服务器支持MCP SSE传输协议');\n                    console.error('- 联系服务器管理员确认配置');\n                    // 使用Headers对象来处理请求头\n                    const headersObj = new Headers(headers);\n                    console.error(`当前请求头: ${JSON.stringify(Object.fromEntries(headersObj.entries()), null, 2)}`);\n                    // 对于412错误，不进行重试，因为这通常是配置问题\n                    return false;\n                }\n                if (is429Error) {\n                    console.warn(`检测到429错误 (${this.config.name})，这通常表示服务器过载或达到速率限制`);\n                    if (attempt < maxRetries) {\n                        // 指数退避延迟\n                        const delay = baseDelay * Math.pow(2, attempt - 1);\n                        console.log(`等待 ${delay}ms 后重试...`);\n                        await new Promise((resolve)=>setTimeout(resolve, delay));\n                        continue;\n                    }\n                }\n                await this.disconnect();\n                if (attempt === maxRetries) {\n                    if (is429Error) {\n                        console.error(`SSE MCP服务器 ${this.config.name} 持续返回429错误，可能是服务器过载或速率限制。请稍后再试。`);\n                    } else {\n                        console.error(`SSE MCP服务器 ${this.config.name} 连接失败，已尝试 ${maxRetries} 次`);\n                        console.error('可能的原因:');\n                        console.error('1. 服务器URL不正确或服务器未运行');\n                        console.error('2. 网络连接问题');\n                        console.error('3. 服务器不支持MCP SSE协议');\n                        console.error('4. 认证或权限问题');\n                        console.error('5. CORS配置问题');\n                        console.error(`错误详情: ${error?.message || '未知错误'}`);\n                    }\n                    return false;\n                }\n            }\n        }\n        return false;\n    }\n    /**\n   * 断开连接\n   */ async disconnect() {\n        try {\n            if (this.client) {\n                await this.client.close();\n                this.client = null;\n            }\n            if (this.transport) {\n                await this.transport.close();\n                this.transport = null;\n            }\n            this.isConnected = false;\n            this.availableTools = [];\n            console.log(`SSE MCP客户端已断开连接: ${this.config.name}`);\n        } catch (error) {\n            console.error(`断开SSE MCP连接时出错 (${this.config.name}):`, error);\n        }\n    }\n    /**\n   * 检查连接状态\n   */ getConnectionStatus() {\n        return this.isConnected;\n    }\n    /**\n   * 刷新可用工具列表\n   */ async refreshTools() {\n        if (!this.client || !this.isConnected) {\n            console.warn(`SSE MCP客户端未连接，无法刷新工具 (${this.config.name})`);\n            return;\n        }\n        try {\n            const response = await this.client.listTools();\n            this.availableTools = response.tools.map((tool)=>{\n                // 清理inputSchema，移除$schema字段以符合Ollama要求\n                const cleanInputSchema = tool.inputSchema ? {\n                    ...tool.inputSchema\n                } : {};\n                if (cleanInputSchema && typeof cleanInputSchema === 'object' && '$schema' in cleanInputSchema) {\n                    delete cleanInputSchema.$schema;\n                }\n                return {\n                    name: tool.name,\n                    description: tool.description || '',\n                    inputSchema: cleanInputSchema\n                };\n            });\n            console.log(`已从SSE MCP服务器获取 ${this.availableTools.length} 个工具 (${this.config.name})`);\n        } catch (error) {\n            console.error(`刷新SSE MCP工具失败 (${this.config.name}):`, error);\n            this.availableTools = [];\n        }\n    }\n    /**\n   * 获取可用工具列表\n   */ getAvailableTools() {\n        return this.availableTools;\n    }\n    /**\n   * 检查工具是否可用\n   */ isToolAvailable(toolName) {\n        return this.availableTools.some((tool)=>tool.name === toolName);\n    }\n    /**\n   * 调用工具\n   */ async callTool(toolName, args) {\n        if (!this.client || !this.isConnected) {\n            throw new Error(`SSE MCP客户端未连接 (${this.config.name})`);\n        }\n        try {\n            console.log(`调用SSE MCP工具: ${toolName} (${this.config.name})`, args);\n            const result = await this.client.callTool({\n                name: toolName,\n                arguments: args\n            });\n            // 确保返回的结果格式正确\n            if (result.content && Array.isArray(result.content)) {\n                return {\n                    content: result.content.map((item)=>({\n                            type: 'text',\n                            text: typeof item === 'string' ? item : typeof item === 'object' && item.text ? item.text : JSON.stringify(item)\n                        }))\n                };\n            } else {\n                return {\n                    content: [\n                        {\n                            type: 'text',\n                            text: typeof result === 'string' ? result : JSON.stringify(result)\n                        }\n                    ]\n                };\n            }\n        } catch (error) {\n            console.error(`SSE MCP工具调用失败 (${this.config.name}):`, error);\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: `工具调用失败: ${error instanceof Error ? error.message : String(error)}`\n                    }\n                ],\n                isError: true\n            };\n        }\n    }\n    /**\n   * 获取服务器配置\n   */ getConfig() {\n        return this.config;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SSEMcpClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp/mcp-client-sse.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp/mcp-client-streamable-http.ts":
/*!***************************************************!*\
  !*** ./src/lib/mcp/mcp-client-streamable-http.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/index.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js\");\n/* harmony import */ var _modelcontextprotocol_sdk_client_streamableHttp_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @modelcontextprotocol/sdk/client/streamableHttp.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js\");\n/**\n * Streamable HTTP MCP客户端实现\n * 支持通过Streamable HTTP连接到远程MCP服务器\n * 这是官方推荐的传输方式，替代已弃用的SSE传输\n */ \n\n/**\n * Streamable HTTP MCP客户端类\n * 负责与远程Streamable HTTP MCP服务器的通信\n */ class StreamableHTTPMcpClient {\n    constructor(config){\n        this.client = null;\n        this.transport = null;\n        this.isConnected = false;\n        this.availableTools = [];\n        this.config = config;\n    }\n    /**\n   * 检测VPN环境\n   */ async detectVPNEnvironment() {\n        try {\n            // 检查常见的VPN环境指标\n            const userAgent = navigator.userAgent;\n            const hasVPNIndicators = /VPN|Proxy|Tunnel/i.test(userAgent);\n            // 检查网络延迟（简单的VPN检测）\n            const startTime = Date.now();\n            await fetch('data:text/plain,', {\n                method: 'HEAD'\n            });\n            const latency = Date.now() - startTime;\n            // 如果延迟超过100ms，可能在使用VPN\n            return hasVPNIndicators || latency > 100;\n        } catch  {\n            return false;\n        }\n    }\n    /**\n   * 使用VPN兼容模式连接\n   */ async connectWithVPNMode() {\n        const originalVPNSetting = this.config.vpnCompatible;\n        try {\n            // 临时启用VPN兼容模式\n            this.config.vpnCompatible = true;\n            const result = await this.connect();\n            return result;\n        } finally{\n            // 恢复原始设置\n            this.config.vpnCompatible = originalVPNSetting;\n        }\n    }\n    /**\n   * 连接到Streamable HTTP MCP服务器\n   */ async connect() {\n        const maxRetries = this.config.retryAttempts || 3;\n        const baseDelay = 1000; // 1秒基础延迟\n        // 检测VPN环境\n        const isVPNDetected = await this.detectVPNEnvironment();\n        const isVPNMode = this.config.vpnCompatible || isVPNDetected;\n        // 根据MCP Streamable HTTP协议规范配置请求头\n        const headers = {\n            'Accept': 'application/json, text/event-stream',\n            'Content-Type': 'application/json',\n            'User-Agent': 'kun-agent-streamable-http-client/1.0.0'\n        };\n        // VPN兼容模式下的特殊配置\n        if (isVPNMode) {\n            headers['X-Forwarded-For'] = '127.0.0.1';\n            headers['X-Real-IP'] = '127.0.0.1';\n            headers['Pragma'] = 'no-cache';\n            headers['Expires'] = '0';\n        } else {\n            // 非VPN模式下添加CORS相关头部\n            headers['Cache-Control'] = 'no-cache';\n            headers['Connection'] = 'keep-alive';\n        }\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                console.log(`正在连接到Streamable HTTP MCP服务器: ${this.config.url} (尝试 ${attempt}/${maxRetries})`);\n                // 创建URL对象\n                const url = new URL(this.config.url);\n                // 重置headers为基础配置\n                Object.keys(headers).forEach((key)=>{\n                    if (![\n                        'Accept',\n                        'Content-Type',\n                        'User-Agent'\n                    ].includes(key)) {\n                        delete headers[key];\n                    }\n                });\n                // 重新设置基础请求头\n                headers['Accept'] = 'application/json, text/event-stream';\n                headers['Content-Type'] = 'application/json';\n                headers['User-Agent'] = 'kun-agent-streamable-http-client/1.0.0';\n                // VPN兼容模式配置\n                if (isVPNMode) {\n                    headers['X-Forwarded-For'] = '127.0.0.1';\n                    headers['X-Real-IP'] = '127.0.0.1';\n                    headers['Pragma'] = 'no-cache';\n                    headers['Expires'] = '0';\n                } else {\n                    headers['Cache-Control'] = 'no-cache';\n                    headers['Connection'] = 'keep-alive';\n                }\n                // 添加MCP协议要求的会话ID头（生成唯一会话ID）\n                const sessionId = `mcp-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n                headers['Mcp-Session-Id'] = sessionId;\n                // 添加MCP协议版本头\n                const protocolVersion = this.config.protocolVersion || '2025-03-26';\n                headers['Mcp-Protocol-Version'] = protocolVersion;\n                // 如果配置中包含API密钥，添加认证头\n                if (this.config.apiKey) {\n                    headers['Authorization'] = `Bearer ${this.config.apiKey}`;\n                }\n                // 合并自定义请求头\n                if (this.config.headers) {\n                    Object.assign(headers, this.config.headers);\n                }\n                // 创建传输配置\n                const transportConfig = {\n                    headers\n                };\n                // VPN兼容模式下的传输配置\n                if (isVPNMode) {\n                    // 延长超时时间\n                    transportConfig.timeout = (this.config.timeout || 15000) * 2;\n                    transportConfig.keepalive = false;\n                    transportConfig.cache = 'no-store';\n                    transportConfig.redirect = 'follow';\n                } else {\n                    // 添加超时配置\n                    if (this.config.timeout) {\n                        transportConfig.timeout = this.config.timeout;\n                    }\n                }\n                // 创建Streamable HTTP传输\n                this.transport = new _modelcontextprotocol_sdk_client_streamableHttp_js__WEBPACK_IMPORTED_MODULE_1__.StreamableHTTPClientTransport(url, transportConfig);\n                // 创建客户端\n                this.client = new _modelcontextprotocol_sdk_client_index_js__WEBPACK_IMPORTED_MODULE_0__.Client({\n                    name: `kun-agent-streamable-http-client-${this.config.name}`,\n                    version: '1.0.0'\n                }, {\n                    capabilities: {\n                        tools: {}\n                    }\n                });\n                // 连接到服务器\n                await this.client.connect(this.transport);\n                this.isConnected = true;\n                // 获取可用工具\n                await this.refreshTools();\n                console.log(`Streamable HTTP MCP客户端连接成功: ${this.config.name}`);\n                return true;\n            } catch (error) {\n                console.error(`Streamable HTTP MCP客户端连接失败 (${this.config.name}) - 尝试 ${attempt}:`, error);\n                // 检查错误类型\n                const is429Error = error?.code === 429 || error?.status === 429 || error?.message?.includes('429') || error?.message?.includes('Too Many Requests');\n                const is412Error = error?.code === 412 || error?.status === 412 || error?.message?.includes('412') || error?.message?.includes('Precondition Failed');\n                const is422Error = error?.code === 422 || error?.status === 422 || error?.message?.includes('422') || error?.message?.includes('Unprocessable Entity');\n                // 检测VPN相关错误\n                const isVPNRelatedError = (is412Error || is422Error) && (error?.message?.toLowerCase().includes('proxy') || error?.message?.toLowerCase().includes('vpn') || error?.message?.toLowerCase().includes('tunnel') || error?.message?.toLowerCase().includes('network') || error?.message?.toLowerCase().includes('timeout'));\n                if (is412Error || is422Error) {\n                    const errorType = is412Error ? '412 (Precondition Failed)' : '422 (Unprocessable Entity)';\n                    console.error(`检测到${errorType}错误 (${this.config.name})，这通常表示:`);\n                    if (isVPNMode) {\n                        console.error('VPN模式下的可能原因:');\n                        console.error('1. VPN服务器与目标服务器之间的网络问题');\n                        console.error('2. VPN节点被目标服务器屏蔽');\n                        console.error('3. VPN协议与服务器不兼容');\n                        console.error('解决建议: 尝试切换VPN节点或关闭VPN');\n                    } else {\n                        console.error('可能的原因:');\n                        console.error('1. 服务器要求特定的认证头或API密钥');\n                        console.error('2. 请求头不符合MCP Streamable HTTP协议要求');\n                        console.error('3. 服务器CORS配置不允许当前域名访问');\n                        console.error('4. 服务器不支持当前的MCP协议版本');\n                        console.error('5. 会话ID格式不正确或服务器不接受会话管理');\n                        console.error('解决建议:');\n                        console.error('- 检查服务器是否需要API密钥认证');\n                        console.error('- 验证服务器CORS配置是否正确');\n                        console.error('- 确认服务器支持MCP Streamable HTTP传输协议');\n                    }\n                    console.error(`当前请求头: ${JSON.stringify(headers, null, 2)}`);\n                    // 对于412和422错误，不进行重试，因为这通常是配置问题\n                    return false;\n                }\n                // 如果检测到VPN相关错误且当前不在VPN模式，尝试VPN兼容模式\n                if (isVPNRelatedError && !isVPNMode && attempt < maxRetries) {\n                    console.log(`检测到VPN相关错误，尝试使用VPN兼容模式重新连接...`);\n                    return await this.connectWithVPNMode();\n                }\n                if (is429Error) {\n                    console.warn(`检测到429错误 (${this.config.name})，这通常表示服务器过载或达到速率限制`);\n                    if (attempt < maxRetries) {\n                        // 指数退避延迟\n                        const delay = baseDelay * Math.pow(2, attempt - 1);\n                        console.log(`等待 ${delay}ms 后重试...`);\n                        await new Promise((resolve)=>setTimeout(resolve, delay));\n                        continue;\n                    }\n                }\n                await this.disconnect();\n                if (attempt === maxRetries) {\n                    if (is429Error) {\n                        console.error(`Streamable HTTP MCP服务器 ${this.config.name} 持续返回429错误，可能是服务器过载或速率限制。请稍后再试。`);\n                    } else {\n                        console.error(`Streamable HTTP MCP服务器 ${this.config.name} 连接失败，已尝试 ${maxRetries} 次`);\n                        console.error('可能的原因:');\n                        console.error('1. 服务器URL不正确或服务器未运行');\n                        console.error('2. 网络连接问题');\n                        console.error('3. 服务器不支持MCP Streamable HTTP协议');\n                        console.error('4. 认证或权限问题');\n                        console.error('5. CORS配置问题');\n                        console.error(`错误详情: ${error?.message || '未知错误'}`);\n                    }\n                    return false;\n                }\n            }\n        }\n        return false;\n    }\n    /**\n   * 断开连接\n   */ async disconnect() {\n        try {\n            if (this.client) {\n                await this.client.close();\n                this.client = null;\n            }\n            if (this.transport) {\n                await this.transport.close();\n                this.transport = null;\n            }\n            this.isConnected = false;\n            this.availableTools = [];\n            console.log(`Streamable HTTP MCP客户端已断开连接: ${this.config.name}`);\n        } catch (error) {\n            console.error(`断开Streamable HTTP MCP连接时出错 (${this.config.name}):`, error);\n        }\n    }\n    /**\n   * 检查连接状态\n   */ getConnectionStatus() {\n        return this.isConnected;\n    }\n    /**\n   * 刷新可用工具列表\n   */ async refreshTools() {\n        if (!this.client || !this.isConnected) {\n            console.warn(`Streamable HTTP MCP客户端未连接，无法刷新工具 (${this.config.name})`);\n            return;\n        }\n        try {\n            const response = await this.client.listTools();\n            this.availableTools = response.tools.map((tool)=>{\n                // 清理inputSchema，移除$schema字段以符合Ollama要求\n                const cleanInputSchema = tool.inputSchema ? {\n                    ...tool.inputSchema\n                } : {};\n                if (cleanInputSchema && typeof cleanInputSchema === 'object' && '$schema' in cleanInputSchema) {\n                    delete cleanInputSchema.$schema;\n                }\n                return {\n                    name: tool.name,\n                    description: tool.description || '',\n                    inputSchema: cleanInputSchema\n                };\n            });\n            console.log(`已从Streamable HTTP MCP服务器获取 ${this.availableTools.length} 个工具 (${this.config.name})`);\n        } catch (error) {\n            console.error(`刷新Streamable HTTP MCP工具失败 (${this.config.name}):`, error);\n            this.availableTools = [];\n        }\n    }\n    /**\n   * 获取可用工具列表\n   */ getAvailableTools() {\n        return this.availableTools;\n    }\n    /**\n   * 检查工具是否可用\n   */ isToolAvailable(toolName) {\n        return this.availableTools.some((tool)=>tool.name === toolName);\n    }\n    /**\n   * 调用工具\n   */ async callTool(toolName, args) {\n        if (!this.client || !this.isConnected) {\n            throw new Error(`Streamable HTTP MCP客户端未连接 (${this.config.name})`);\n        }\n        try {\n            console.log(`调用Streamable HTTP MCP工具: ${toolName} (${this.config.name})`, args);\n            const result = await this.client.callTool({\n                name: toolName,\n                arguments: args\n            });\n            // 确保返回的结果格式正确\n            if (result.content && Array.isArray(result.content)) {\n                return {\n                    content: result.content.map((item)=>({\n                            type: 'text',\n                            text: typeof item === 'string' ? item : typeof item === 'object' && item.text ? item.text : JSON.stringify(item)\n                        }))\n                };\n            } else {\n                return {\n                    content: [\n                        {\n                            type: 'text',\n                            text: typeof result === 'string' ? result : JSON.stringify(result)\n                        }\n                    ]\n                };\n            }\n        } catch (error) {\n            console.error(`Streamable HTTP MCP工具调用失败 (${this.config.name}):`, error);\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: `工具调用失败: ${error instanceof Error ? error.message : String(error)}`\n                    }\n                ],\n                isError: true\n            };\n        }\n    }\n    /**\n   * 获取服务器配置\n   */ getConfig() {\n        return this.config;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StreamableHTTPMcpClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp/mcp-client-streamable-http.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp/mcp-client.ts":
/*!***********************************!*\
  !*** ./src/lib/mcp/mcp-client.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   executeMcpTool: () => (/* binding */ executeMcpTool),\n/* harmony export */   getMcpTools: () => (/* binding */ getMcpTools),\n/* harmony export */   initializeMcpClient: () => (/* binding */ initializeMcpClient),\n/* harmony export */   mcpClient: () => (/* binding */ mcpClient)\n/* harmony export */ });\n/**\r\n * MCP客户端实现\r\n * 使用官方TypeScript SDK创建MCP客户端，通过stdio与MCP服务器通信\r\n */ // 这个文件只能在服务器端使用，不能在浏览器端导入\n// 浏览器端应该通过API路由与MCP服务器通信\n// MCP工具接口\n/**\r\n * 浏览器端MCP客户端代理\r\n * 通过API路由与服务器端MCP功能通信\r\n */ class McpClientProxy {\n    /**\r\n   * 连接到MCP服务器（通过API）\r\n   */ async connect() {\n        try {\n            const response = await fetch('/api/mcp/status');\n            const data = await response.json();\n            this.isConnected = data.status === 'connected';\n            if (this.isConnected) {\n                await this.refreshTools();\n            }\n            return this.isConnected;\n        } catch (error) {\n            console.error('MCP连接检查失败:', error);\n            this.isConnected = false;\n            return false;\n        }\n    }\n    /**\r\n   * 断开连接\r\n   */ async disconnect() {\n        this.isConnected = false;\n        this.availableTools = [];\n    }\n    /**\r\n   * 检查是否已连接\r\n   */ isClientConnected() {\n        return this.isConnected;\n    }\n    /**\r\n   * 刷新工具列表\r\n   */ async refreshTools() {\n        try {\n            const response = await fetch('/api/mcp/tools');\n            const data = await response.json();\n            if (data.success && data.tools) {\n                this.availableTools = data.tools;\n            }\n            return this.availableTools;\n        } catch (error) {\n            console.error('刷新工具列表失败:', error);\n            return [];\n        }\n    }\n    /**\r\n   * 获取可用工具列表\r\n   */ getAvailableTools() {\n        return this.availableTools;\n    }\n    /**\r\n   * 检查工具是否可用\r\n   */ isToolAvailable(toolName) {\n        return this.availableTools.some((tool)=>tool.name === toolName);\n    }\n    /**\r\n   * 调用工具\r\n   */ async callTool(toolCall) {\n        try {\n            const response = await fetch('/api/mcp/call-tool', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    name: toolCall.name,\n                    arguments: toolCall.arguments,\n                    serverName: toolCall.serverName\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                return {\n                    content: [\n                        {\n                            type: 'text',\n                            text: data.result || '工具执行成功'\n                        }\n                    ],\n                    isError: false\n                };\n            } else {\n                return {\n                    content: [\n                        {\n                            type: 'text',\n                            text: data.error || '工具执行失败'\n                        }\n                    ],\n                    isError: true\n                };\n            }\n        } catch (error) {\n            console.error('工具调用失败:', error);\n            return {\n                content: [\n                    {\n                        type: 'text',\n                        text: `工具调用失败: ${error instanceof Error ? error.message : '未知错误'}`\n                    }\n                ],\n                isError: true\n            };\n        }\n    }\n    /**\r\n   * 批量调用工具\r\n   */ async callTools(toolCalls) {\n        const results = [];\n        for (const toolCall of toolCalls){\n            const result = await this.callTool(toolCall);\n            results.push(result);\n        }\n        return results;\n    }\n    constructor(){\n        this.availableTools = [];\n        this.isConnected = false;\n    }\n}\n// 导出单例实例\nconst mcpClient = new McpClientProxy();\n/**\r\n * 初始化MCP客户端\r\n */ async function initializeMcpClient() {\n    try {\n        const success = await mcpClient.connect();\n        if (success) {\n            console.log('MCP客户端初始化成功');\n        } else {\n            console.warn('MCP客户端初始化失败');\n        }\n        return success;\n    } catch (error) {\n        console.error('MCP客户端初始化出错:', error);\n        return false;\n    }\n}\n/**\r\n * 获取MCP工具列表\r\n */ async function getMcpTools() {\n    if (!mcpClient.isClientConnected()) {\n        return [];\n    }\n    return mcpClient.getAvailableTools();\n}\n/**\r\n * 执行MCP工具调用\r\n */ async function executeMcpTool(toolCall) {\n    return await mcpClient.callTool(toolCall);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp/mcp-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp/mcp-multi-server-client.ts":
/*!************************************************!*\
  !*** ./src/lib/mcp/mcp-multi-server-client.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   multiServerMcpClient: () => (/* binding */ multiServerMcpClient)\n/* harmony export */ });\n/* harmony import */ var _mcp_client_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mcp-client-server */ \"(rsc)/./src/lib/mcp/mcp-client-server.ts\");\n/* harmony import */ var _mcp_client_sse__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mcp-client-sse */ \"(rsc)/./src/lib/mcp/mcp-client-sse.ts\");\n/* harmony import */ var _mcp_client_streamable_http__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./mcp-client-streamable-http */ \"(rsc)/./src/lib/mcp/mcp-client-streamable-http.ts\");\n/**\n * 多服务器MCP客户端管理器\n * 支持同时管理本地stdio、远程SSE和Streamable HTTP MCP服务器\n */ \n\n\n/**\n * 多服务器MCP客户端管理器\n * 统一管理多个MCP服务器连接\n */ class MultiServerMcpClient {\n    constructor(){\n        this.stdioClient = null;\n        this.sseClients = new Map();\n        this.streamableHTTPClients = new Map();\n        this.config = {};\n        // 初始化本地stdio客户端\n        this.stdioClient = new _mcp_client_server__WEBPACK_IMPORTED_MODULE_0__.McpServerClient();\n    }\n    /**\n   * 设置服务器配置\n   */ setConfig(config) {\n        this.config = config;\n    }\n    /**\n   * 连接到所有配置的服务器\n   */ async connectAll() {\n        const results = {};\n        // 连接本地stdio服务器（如果存在）\n        if (this.stdioClient) {\n            try {\n                const connected = await this.stdioClient.connect();\n                results['local'] = connected;\n            } catch (error) {\n                console.error('本地stdio MCP服务器连接失败:', error);\n                results['local'] = false;\n            }\n        }\n        // 连接所有配置的远程服务器\n        console.log('开始连接远程MCP服务器，配置数量:', Object.keys(this.config).length);\n        console.log('配置详情:', this.config);\n        for (const [serverName, serverConfig] of Object.entries(this.config)){\n            console.log(`处理服务器: ${serverName}, 配置:`, serverConfig);\n            // 检查服务器是否被禁用\n            if (serverConfig.enabled === false) {\n                console.log(`MCP服务器 ${serverName} 已被禁用，跳过连接`);\n                results[serverName] = false;\n                continue;\n            }\n            if (serverConfig.type === 'sse' && serverConfig.url) {\n                try {\n                    const sseConfig = {\n                        name: serverName,\n                        url: serverConfig.url,\n                        type: 'sse',\n                        apiKey: serverConfig.apiKey,\n                        headers: serverConfig.headers,\n                        timeout: serverConfig.timeout,\n                        retryAttempts: serverConfig.retryAttempts,\n                        protocolVersion: serverConfig.protocolVersion\n                    };\n                    console.log(`尝试连接SSE服务器: ${serverName}, URL: ${serverConfig.url}`);\n                    const sseClient = new _mcp_client_sse__WEBPACK_IMPORTED_MODULE_1__[\"default\"](sseConfig);\n                    const connected = await sseClient.connect();\n                    if (connected) {\n                        this.sseClients.set(serverName, sseClient);\n                        console.log(`SSE MCP服务器 ${serverName} 连接成功`);\n                        // 获取并显示工具\n                        const tools = sseClient.getAvailableTools();\n                        console.log(`服务器 ${serverName} 提供的工具:`, tools.map((t)=>t.name));\n                    } else {\n                        console.warn(`SSE MCP服务器 ${serverName} 连接失败，将跳过此服务器`);\n                    }\n                    results[serverName] = connected;\n                } catch (error) {\n                    console.error(`SSE MCP服务器连接失败 (${serverName}):`, error);\n                    results[serverName] = false;\n                }\n            } else if (serverConfig.type === 'streamable-http' && serverConfig.url) {\n                try {\n                    const streamableHTTPConfig = {\n                        name: serverName,\n                        url: serverConfig.url,\n                        type: 'streamable-http',\n                        apiKey: serverConfig.apiKey,\n                        headers: serverConfig.headers,\n                        timeout: serverConfig.timeout,\n                        retryAttempts: serverConfig.retryAttempts,\n                        protocolVersion: serverConfig.protocolVersion,\n                        vpnCompatible: serverConfig.vpnCompatible\n                    };\n                    console.log(`尝试连接Streamable HTTP服务器: ${serverName}, URL: ${serverConfig.url}`);\n                    const streamableHTTPClient = new _mcp_client_streamable_http__WEBPACK_IMPORTED_MODULE_2__[\"default\"](streamableHTTPConfig);\n                    const connected = await streamableHTTPClient.connect();\n                    if (connected) {\n                        this.streamableHTTPClients.set(serverName, streamableHTTPClient);\n                        console.log(`Streamable HTTP MCP服务器 ${serverName} 连接成功`);\n                        // 获取并显示工具\n                        const tools = streamableHTTPClient.getAvailableTools();\n                        console.log(`服务器 ${serverName} 提供的工具:`, tools.map((t)=>t.name));\n                    } else {\n                        console.warn(`Streamable HTTP MCP服务器 ${serverName} 连接失败，将跳过此服务器`);\n                    }\n                    results[serverName] = connected;\n                } catch (error) {\n                    console.error(`Streamable HTTP MCP服务器连接失败 (${serverName}):`, error);\n                    results[serverName] = false;\n                }\n            } else {\n                console.log(`跳过服务器 ${serverName}: type=${serverConfig.type}, url=${serverConfig.url}`);\n            }\n        }\n        return results;\n    }\n    /**\n   * 连接单个服务器\n   */ async connectServer(serverName) {\n        const serverConfig = this.config[serverName];\n        if (!serverConfig) {\n            console.error(`服务器配置不存在: ${serverName}`);\n            return false;\n        }\n        console.log(`连接单个服务器: ${serverName}, 配置:`, serverConfig);\n        // 检查服务器是否被禁用\n        if (serverConfig.enabled === false) {\n            console.log(`MCP服务器 ${serverName} 已被禁用，跳过连接`);\n            return false;\n        }\n        try {\n            if (serverConfig.type === 'sse' && serverConfig.url) {\n                const sseConfig = {\n                    name: serverName,\n                    url: serverConfig.url,\n                    type: 'sse',\n                    apiKey: serverConfig.apiKey,\n                    headers: serverConfig.headers,\n                    timeout: serverConfig.timeout,\n                    retryAttempts: serverConfig.retryAttempts,\n                    protocolVersion: serverConfig.protocolVersion\n                };\n                console.log(`尝试连接SSE服务器: ${serverName}, URL: ${serverConfig.url}`);\n                const sseClient = new _mcp_client_sse__WEBPACK_IMPORTED_MODULE_1__[\"default\"](sseConfig);\n                const connected = await sseClient.connect();\n                if (connected) {\n                    this.sseClients.set(serverName, sseClient);\n                    console.log(`SSE MCP服务器 ${serverName} 连接成功`);\n                    // 获取并显示工具\n                    const tools = sseClient.getAvailableTools();\n                    console.log(`服务器 ${serverName} 提供的工具:`, tools.map((t)=>t.name));\n                } else {\n                    console.warn(`SSE MCP服务器 ${serverName} 连接失败`);\n                }\n                return connected;\n            } else if (serverConfig.type === 'streamable-http' && serverConfig.url) {\n                const streamableHTTPConfig = {\n                    name: serverName,\n                    url: serverConfig.url,\n                    type: 'streamable-http',\n                    apiKey: serverConfig.apiKey,\n                    headers: serverConfig.headers,\n                    timeout: serverConfig.timeout,\n                    retryAttempts: serverConfig.retryAttempts,\n                    protocolVersion: serverConfig.protocolVersion,\n                    vpnCompatible: serverConfig.vpnCompatible\n                };\n                console.log(`尝试连接Streamable HTTP服务器: ${serverName}, URL: ${serverConfig.url}`);\n                const streamableHTTPClient = new _mcp_client_streamable_http__WEBPACK_IMPORTED_MODULE_2__[\"default\"](streamableHTTPConfig);\n                const connected = await streamableHTTPClient.connect();\n                if (connected) {\n                    this.streamableHTTPClients.set(serverName, streamableHTTPClient);\n                    console.log(`Streamable HTTP MCP服务器 ${serverName} 连接成功`);\n                    // 获取并显示工具\n                    const tools = streamableHTTPClient.getAvailableTools();\n                    console.log(`服务器 ${serverName} 提供的工具:`, tools.map((t)=>t.name));\n                } else {\n                    console.warn(`Streamable HTTP MCP服务器 ${serverName} 连接失败`);\n                }\n                return connected;\n            } else {\n                console.log(`跳过服务器 ${serverName}: type=${serverConfig.type}, url=${serverConfig.url}`);\n                return false;\n            }\n        } catch (error) {\n            console.error(`连接MCP服务器失败 (${serverName}):`, error);\n            return false;\n        }\n    }\n    /**\n   * 智能连接：只连接包含指定工具的服务器\n   */ async connectForTool(toolName) {\n        // 首先检查本地工具\n        if (this.stdioClient && this.stdioClient.isToolAvailable(toolName)) {\n            if (!this.stdioClient.isClientConnected()) {\n                const connected = await this.stdioClient.connect();\n                if (connected) {\n                    console.log(`为工具 ${toolName} 连接了本地服务器`);\n                    return 'local';\n                }\n            } else {\n                return 'local';\n            }\n        }\n        // 检查已连接的远程服务器\n        for (const [serverName, sseClient] of Array.from(this.sseClients.entries())){\n            if (sseClient.getConnectionStatus() && sseClient.isToolAvailable(toolName)) {\n                console.log(`工具 ${toolName} 在已连接的SSE服务器 ${serverName} 中找到`);\n                return serverName;\n            }\n        }\n        for (const [serverName, streamableHTTPClient] of Array.from(this.streamableHTTPClients.entries())){\n            if (streamableHTTPClient.getConnectionStatus() && streamableHTTPClient.isToolAvailable(toolName)) {\n                console.log(`工具 ${toolName} 在已连接的Streamable HTTP服务器 ${serverName} 中找到`);\n                return serverName;\n            }\n        }\n        // 如果没有找到，尝试连接可能包含该工具的服务器\n        // 这里我们需要连接所有未连接的服务器来查找工具\n        for (const [serverName, serverConfig] of Object.entries(this.config)){\n            // 检查服务器是否被禁用\n            if (serverConfig.enabled === false) {\n                continue;\n            }\n            // 检查服务器是否已连接\n            const isSSEConnected = this.sseClients.has(serverName) && this.sseClients.get(serverName)?.getConnectionStatus();\n            const isStreamableHTTPConnected = this.streamableHTTPClients.has(serverName) && this.streamableHTTPClients.get(serverName)?.getConnectionStatus();\n            if (!isSSEConnected && !isStreamableHTTPConnected) {\n                console.log(`尝试连接服务器 ${serverName} 来查找工具 ${toolName}`);\n                const connected = await this.connectServer(serverName);\n                if (connected) {\n                    // 检查新连接的服务器是否有该工具\n                    if (this.isToolAvailable(toolName, serverName)) {\n                        console.log(`在新连接的服务器 ${serverName} 中找到工具 ${toolName}`);\n                        return serverName;\n                    }\n                }\n            }\n        }\n        console.log(`未找到工具 ${toolName} 对应的服务器`);\n        return null;\n    }\n    /**\n   * 断开指定服务器的连接\n   */ async disconnectServer(serverName) {\n        if (serverName === 'local') {\n            // 本地服务器不支持断开连接\n            console.log('本地服务器不支持断开连接操作');\n            return;\n        }\n        // 断开SSE服务器连接\n        const sseClient = this.sseClients.get(serverName);\n        if (sseClient) {\n            await sseClient.disconnect();\n            this.sseClients.delete(serverName);\n            console.log(`已断开SSE服务器 ${serverName} 的连接`);\n            return;\n        }\n        // 断开Streamable HTTP服务器连接\n        const streamableHTTPClient = this.streamableHTTPClients.get(serverName);\n        if (streamableHTTPClient) {\n            await streamableHTTPClient.disconnect();\n            this.streamableHTTPClients.delete(serverName);\n            console.log(`已断开Streamable HTTP服务器 ${serverName} 的连接`);\n            return;\n        }\n        console.log(`服务器 ${serverName} 不存在或未连接`);\n    }\n    /**\n   * 断开所有连接\n   */ async disconnectAll() {\n        // 断开本地stdio连接\n        if (this.stdioClient) {\n            await this.stdioClient.disconnect();\n        }\n        // 断开所有SSE连接\n        for (const [serverName, sseClient] of Array.from(this.sseClients.entries())){\n            await sseClient.disconnect();\n        }\n        this.sseClients.clear();\n        // 断开所有Streamable HTTP连接\n        for (const [serverName, streamableHTTPClient] of Array.from(this.streamableHTTPClients.entries())){\n            await streamableHTTPClient.disconnect();\n        }\n        this.streamableHTTPClients.clear();\n    }\n    /**\n   * 获取所有服务器的连接状态\n   */ getConnectionStatus() {\n        const status = {};\n        // 本地stdio服务器状态\n        if (this.stdioClient) {\n            status['local'] = this.stdioClient.isClientConnected();\n        }\n        // SSE服务器状态\n        for (const [serverName, sseClient] of Array.from(this.sseClients.entries())){\n            status[serverName] = sseClient.getConnectionStatus();\n        }\n        // Streamable HTTP服务器状态\n        for (const [serverName, streamableHTTPClient] of Array.from(this.streamableHTTPClients.entries())){\n            status[serverName] = streamableHTTPClient.getConnectionStatus();\n        }\n        return status;\n    }\n    /**\n   * 刷新所有服务器的工具列表\n   */ async refreshAllTools() {\n        // 刷新本地stdio工具\n        if (this.stdioClient && this.stdioClient.isClientConnected()) {\n            await this.stdioClient.refreshTools();\n        }\n        // 刷新所有SSE服务器工具\n        for (const [serverName, sseClient] of Array.from(this.sseClients.entries())){\n            if (sseClient.getConnectionStatus()) {\n                await sseClient.refreshTools();\n            }\n        }\n        // 刷新所有Streamable HTTP服务器工具\n        for (const [serverName, streamableHTTPClient] of Array.from(this.streamableHTTPClients.entries())){\n            if (streamableHTTPClient.getConnectionStatus()) {\n                await streamableHTTPClient.refreshTools();\n            }\n        }\n    }\n    /**\n   * 获取所有可用工具（包含服务器信息）\n   */ getAllAvailableTools() {\n        const allTools = [];\n        // 添加本地stdio工具\n        if (this.stdioClient && this.stdioClient.isClientConnected()) {\n            const localTools = this.stdioClient.getAvailableTools();\n            allTools.push(...localTools.map((tool)=>({\n                    ...tool,\n                    serverName: 'local',\n                    serverType: 'stdio'\n                })));\n        }\n        // 添加所有SSE服务器工具\n        for (const [serverName, sseClient] of Array.from(this.sseClients.entries())){\n            if (sseClient.getConnectionStatus()) {\n                const sseTools = sseClient.getAvailableTools();\n                allTools.push(...sseTools.map((tool)=>({\n                        ...tool,\n                        serverName,\n                        serverType: 'sse'\n                    })));\n            }\n        }\n        // 添加所有Streamable HTTP服务器工具\n        for (const [serverName, streamableHTTPClient] of Array.from(this.streamableHTTPClients.entries())){\n            if (streamableHTTPClient.getConnectionStatus()) {\n                const streamableHTTPTools = streamableHTTPClient.getAvailableTools();\n                allTools.push(...streamableHTTPTools.map((tool)=>({\n                        ...tool,\n                        serverName,\n                        serverType: 'streamable-http'\n                    })));\n            }\n        }\n        return allTools;\n    }\n    /**\n   * 检查工具是否可用\n   */ isToolAvailable(toolName, serverName) {\n        if (serverName) {\n            // 检查特定服务器\n            if (serverName === 'local' && this.stdioClient) {\n                return this.stdioClient.isToolAvailable(toolName);\n            }\n            const sseClient = this.sseClients.get(serverName);\n            if (sseClient) {\n                return sseClient.isToolAvailable(toolName);\n            }\n            const streamableHTTPClient = this.streamableHTTPClients.get(serverName);\n            if (streamableHTTPClient) {\n                return streamableHTTPClient.isToolAvailable(toolName);\n            }\n            return false;\n        } else {\n            // 检查所有服务器\n            return this.getAllAvailableTools().some((tool)=>tool.name === toolName);\n        }\n    }\n    /**\n   * 调用工具\n   */ async callTool(toolName, args, serverName) {\n        // 如果指定了服务器名称\n        if (serverName) {\n            if (serverName === 'local' && this.stdioClient) {\n                return await this.stdioClient.callTool({\n                    name: toolName,\n                    arguments: args\n                });\n            }\n            const sseClient = this.sseClients.get(serverName);\n            if (sseClient) {\n                return await sseClient.callTool(toolName, args);\n            }\n            const streamableHTTPClient = this.streamableHTTPClients.get(serverName);\n            if (streamableHTTPClient) {\n                return await streamableHTTPClient.callTool(toolName, args);\n            }\n            throw new Error(`服务器 '${serverName}' 不存在或未连接`);\n        }\n        // 自动查找工具所在的服务器\n        const allTools = this.getAllAvailableTools();\n        const tool = allTools.find((t)=>t.name === toolName);\n        if (!tool) {\n            throw new Error(`工具 '${toolName}' 不存在`);\n        }\n        // 递归调用，指定服务器名称\n        return await this.callTool(toolName, args, tool.serverName);\n    }\n    /**\n   * 获取特定服务器的工具列表\n   */ getToolsByServer(serverName) {\n        if (serverName === 'local' && this.stdioClient) {\n            return this.stdioClient.getAvailableTools();\n        }\n        const sseClient = this.sseClients.get(serverName);\n        if (sseClient) {\n            return sseClient.getAvailableTools();\n        }\n        return [];\n    }\n    /**\n   * 获取服务器列表\n   */ getServerList() {\n        const servers = [\n            'local'\n        ];\n        servers.push(...Array.from(this.sseClients.keys()));\n        return servers;\n    }\n}\n// 导出单例实例\nconst multiServerMcpClient = new MultiServerMcpClient();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MultiServerMcpClient);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp/mcp-multi-server-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ollama.ts":
/*!***************************!*\
  !*** ./src/lib/ollama.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OllamaClient: () => (/* binding */ OllamaClient),\n/* harmony export */   ollamaClient: () => (/* binding */ ollamaClient)\n/* harmony export */ });\n// Ollama API 客户端\nconst OLLAMA_BASE_URL = 'http://localhost:11434';\nclass OllamaClient {\n    constructor(baseUrl = OLLAMA_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n    /**\n   * 获取本地可用的模型列表\n   */ async getModels() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            const data = await response.json();\n            return data.models || [];\n        } catch (error) {\n            console.error('获取模型列表失败:', error);\n            throw new Error('无法连接到Ollama服务，请确保Ollama正在运行');\n        }\n    }\n    /**\n   * 发送聊天请求（非流式）\n   */ async chat(request) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/chat`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...request,\n                    stream: false\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            return await response.json();\n        } catch (error) {\n            console.error('聊天请求失败:', error);\n            throw new Error('聊天请求失败，请检查网络连接和Ollama服务状态');\n        }\n    }\n    /**\n   * 发送流式聊天请求\n   */ async *chatStream(request) {\n        try {\n            // console.log('Ollama chatStream 请求:', JSON.stringify(request, null, 2));\n            const response = await fetch(`${this.baseUrl}/api/chat`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    ...request,\n                    stream: true\n                })\n            });\n            // console.log('Ollama 响应状态:', response.status, response.statusText);\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('Ollama API 错误响应:', response.status, response.statusText, errorText);\n                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);\n            }\n            if (!response.body) {\n                throw new Error('响应体为空');\n            }\n            const reader = response.body.getReader();\n            const decoder = new TextDecoder();\n            let buffer = '';\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    buffer += decoder.decode(value, {\n                        stream: true\n                    });\n                    const lines = buffer.split('\\n');\n                    // 保留最后一行（可能不完整）\n                    buffer = lines.pop() || '';\n                    for (const line of lines){\n                        const trimmedLine = line.trim();\n                        if (trimmedLine) {\n                            try {\n                                const data = JSON.parse(trimmedLine);\n                                yield data;\n                                // 如果收到完成标志，结束生成\n                                if (data.done) {\n                                    return;\n                                }\n                            } catch (parseError) {\n                                console.warn('解析JSON失败:', parseError, '原始数据:', trimmedLine);\n                            }\n                        }\n                    }\n                }\n                // 处理缓冲区中剩余的数据\n                if (buffer.trim()) {\n                    try {\n                        const data = JSON.parse(buffer.trim());\n                        yield data;\n                    } catch (parseError) {\n                        console.warn('解析最后的JSON失败:', parseError);\n                    }\n                }\n            } finally{\n                reader.releaseLock();\n            }\n        } catch (error) {\n            console.error('流式聊天请求失败:', error);\n            if (error instanceof Error) {\n                throw error; // 保持原始错误信息\n            } else {\n                throw new Error('流式聊天请求失败，请检查网络连接和Ollama服务状态');\n            }\n        }\n    }\n    /**\n   * 检查Ollama服务是否可用\n   */ async isAvailable() {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/tags`, {\n                method: 'GET',\n                signal: AbortSignal.timeout(5000)\n            });\n            return response.ok;\n        } catch  {\n            return false;\n        }\n    }\n    /**\n   * 检查指定模型是否已加载到内存中\n   */ async isModelLoaded(modelName) {\n        try {\n            const response = await fetch(`${this.baseUrl}/api/ps`, {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                console.error('获取模型状态失败:', response.status, response.statusText);\n                return false;\n            }\n            const data = await response.json();\n            const loadedModels = data.models || [];\n            // 检查指定模型是否在已加载的模型列表中\n            return loadedModels.some((model)=>model.name === modelName);\n        } catch (error) {\n            console.error('检查模型加载状态失败:', error);\n            return false;\n        }\n    }\n    /**\n   * 格式化模型大小\n   */ static formatModelSize(bytes) {\n        const units = [\n            'B',\n            'KB',\n            'MB',\n            'GB',\n            'TB'\n        ];\n        let size = bytes;\n        let unitIndex = 0;\n        while(size >= 1024 && unitIndex < units.length - 1){\n            size /= 1024;\n            unitIndex++;\n        }\n        return `${size.toFixed(1)} ${units[unitIndex]}`;\n    }\n    /**\n   * 格式化模型名称（移除标签）\n   */ static formatModelName(name) {\n        return name.split(':')[0];\n    }\n}\n// 默认客户端实例\nconst ollamaClient = new OllamaClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ollama.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/tools.ts":
/*!**************************!*\
  !*** ./src/lib/tools.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToolExecutor: () => (/* binding */ ToolExecutor),\n/* harmony export */   availableTools: () => (/* binding */ availableTools),\n/* harmony export */   getAllAvailableTools: () => (/* binding */ getAllAvailableTools),\n/* harmony export */   getToolsByNames: () => (/* binding */ getToolsByNames),\n/* harmony export */   initializeMcpTools: () => (/* binding */ initializeMcpTools),\n/* harmony export */   isToolAvailable: () => (/* binding */ isToolAvailable),\n/* harmony export */   loadAvailableTools: () => (/* binding */ loadAvailableTools),\n/* harmony export */   refreshMcpTools: () => (/* binding */ refreshMcpTools),\n/* harmony export */   testTool: () => (/* binding */ testTool)\n/* harmony export */ });\n/* harmony import */ var _mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mcp/mcp-client */ \"(rsc)/./src/lib/mcp/mcp-client.ts\");\n\n/**\n * 清理参数以符合Ollama要求\n * 移除Ollama不支持的JSON Schema字段\n */ function cleanParametersForOllama(inputSchema) {\n    if (!inputSchema || typeof inputSchema !== 'object') {\n        return {\n            type: 'object',\n            properties: {},\n            required: []\n        };\n    }\n    const cleaned = {\n        ...inputSchema\n    };\n    // 移除Ollama不支持的字段\n    delete cleaned.$schema;\n    delete cleaned.$id;\n    delete cleaned.$ref;\n    delete cleaned.definitions;\n    delete cleaned.additionalProperties;\n    // 确保必需字段存在\n    if (!cleaned.type) cleaned.type = 'object';\n    if (!cleaned.properties) cleaned.properties = {};\n    if (!cleaned.required) cleaned.required = [];\n    return cleaned;\n}\n/**\n * 将MCP工具转换为Ollama格式\n */ function convertMcpToolToOllamaFormat(mcpTool) {\n    return {\n        type: 'function',\n        function: {\n            name: mcpTool.name,\n            description: mcpTool.description || '',\n            parameters: cleanParametersForOllama(mcpTool.inputSchema)\n        },\n        // 保留服务器信息用于分类\n        serverName: mcpTool.serverName,\n        serverType: mcpTool.serverType\n    };\n}\n/**\n * 验证工具是否符合Ollama要求\n */ function validateOllamaTool(tool) {\n    try {\n        return tool.type === 'function' && typeof tool.function.name === 'string' && tool.function.name.length > 0 && typeof tool.function.description === 'string' && tool.function.parameters && tool.function.parameters.type === 'object' && Array.isArray(tool.function.parameters.required);\n    } catch (error) {\n        console.warn('工具验证失败:', error);\n        return false;\n    }\n}\n// 保留测试工具用于模型兼容性检查\nconst testTool = {\n    type: 'function',\n    function: {\n        name: 'test_tool',\n        description: '测试工具调用功能',\n        parameters: {\n            type: 'object',\n            properties: {\n                message: {\n                    type: 'string',\n                    description: '测试消息'\n                }\n            },\n            required: [\n                'message'\n            ]\n        }\n    }\n};\n// 所有可用工具的集合 - 现在从MCP服务器动态获取\nlet availableTools = [];\n/**\n * 从MCP服务器获取可用工具列表\n */ async function loadAvailableTools() {\n    try {\n        // 确保MCP客户端已连接\n        if (!_mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__.mcpClient.isClientConnected()) {\n            const connected = await _mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__.mcpClient.connect();\n            if (!connected) {\n                console.warn('无法连接到MCP服务器，使用空工具列表');\n                return [];\n            }\n        }\n        // 获取MCP工具并转换为Ollama工具格式\n        const mcpTools = await _mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__.mcpClient.getAvailableTools();\n        availableTools = mcpTools.map((tool)=>convertMcpToolToOllamaFormat(tool)).filter((tool)=>validateOllamaTool(tool));\n        console.log(`已从MCP服务器加载 ${availableTools.length} 个工具`);\n        return availableTools;\n    } catch (error) {\n        console.error('加载MCP工具失败:', error);\n        return [];\n    }\n}\n// 根据工具名称获取工具定义\nasync function getToolsByNames(toolNames) {\n    console.log('getToolsByNames 被调用，请求的工具:', toolNames);\n    // 在服务器端，从mcpServerClient和多服务器客户端获取工具\n    if (true) {\n        const { mcpServerClient } = __webpack_require__(/*! ./mcp/mcp-client-server */ \"(rsc)/./src/lib/mcp/mcp-client-server.ts\");\n        const { multiServerMcpClient } = __webpack_require__(/*! ./mcp/mcp-multi-server-client */ \"(rsc)/./src/lib/mcp/mcp-multi-server-client.ts\");\n        const allTools = [];\n        const toolMap = new Map(); // 用于去重\n        // 首先尝试从数据库直接获取工具\n        try {\n            console.log('尝试从数据库获取工具...');\n            const response = await fetch('http://localhost:3000/api/mcp/tools', {\n                method: 'GET',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success && data.tools && data.tools.length > 0) {\n                    console.log('从数据库获取到工具:', data.tools.map((t)=>t.name));\n                    const dbTools = data.tools.map((tool)=>convertMcpToolToOllamaFormat(tool)).filter((tool)=>validateOllamaTool(tool));\n                    dbTools.forEach((tool)=>{\n                        toolMap.set(tool.function.name, tool);\n                    });\n                    console.log('从数据库转换的工具:', dbTools.map((t)=>t.function.name));\n                }\n            }\n        } catch (error) {\n            console.error('从数据库获取工具失败:', error);\n        }\n        // 1. 获取原有MCP工具\n        try {\n            const mcpTools = mcpServerClient.getAvailableTools();\n            const serverTools = mcpTools.map((tool)=>convertMcpToolToOllamaFormat(tool)).filter((tool)=>validateOllamaTool(tool));\n            // 添加到工具映射中进行去重\n            serverTools.forEach((tool)=>{\n                toolMap.set(tool.function.name, tool);\n            });\n            console.log('从原有MCP服务器获取到工具:', serverTools.map((t)=>t.function.name));\n        } catch (error) {\n            console.error('获取原有MCP工具失败:', error);\n        }\n        // 2. 获取多服务器MCP工具\n        try {\n            // 首先加载MCP配置\n            console.log('加载MCP服务器配置...');\n            const fs = (__webpack_require__(/*! fs */ \"fs\").promises);\n            const path = __webpack_require__(/*! path */ \"path\");\n            const configPath = path.join(process.cwd(), 'mcp-servers.json');\n            let config = {};\n            try {\n                const configData = await fs.readFile(configPath, 'utf-8');\n                const parsedConfig = JSON.parse(configData);\n                config = parsedConfig.mcpServers || {};\n                console.log('加载的MCP配置:', Object.keys(config));\n            } catch (configError) {\n                console.warn('无法加载MCP配置文件:', configError);\n            }\n            // 设置配置，但不自动连接所有服务器\n            multiServerMcpClient.setConfig(config);\n            console.log('设置多服务器客户端配置，检查已连接的服务器...');\n            // 只获取已连接服务器的工具，不强制连接所有服务器\n            const connectionStatus = multiServerMcpClient.getConnectionStatus();\n            const hasConnectedServers = Object.values(connectionStatus).some((status)=>status);\n            let multiServerTools = [];\n            if (hasConnectedServers) {\n                // 如果有已连接的服务器，获取它们的工具\n                multiServerTools = multiServerMcpClient.getAllAvailableTools();\n                console.log('从已连接的服务器获取到的工具:', multiServerTools);\n            } else {\n                console.log('没有已连接的MCP服务器，跳过工具获取');\n            }\n            const convertedMultiServerTools = multiServerTools.map((tool)=>convertMcpToolToOllamaFormat(tool)).filter((tool)=>validateOllamaTool(tool));\n            // 添加到工具映射中进行去重\n            convertedMultiServerTools.forEach((tool)=>{\n                toolMap.set(tool.function.name, tool);\n            });\n            console.log('转换后的多服务器工具:', convertedMultiServerTools.map((t)=>t.function.name));\n        } catch (error) {\n            console.error('获取多服务器MCP工具失败:', error);\n        }\n        // 将去重后的工具转换为数组\n        allTools.push(...Array.from(toolMap.values()));\n        console.log('所有可用工具:', allTools.map((t)=>t.function.name));\n        // 如果从数据库获取到了工具，优先使用数据库中的工具\n        if (toolMap.size > 0) {\n            console.log('使用数据库中的工具，工具数量:', toolMap.size);\n            // 过滤用户请求的工具\n            const filteredTools = toolNames.map((name)=>toolMap.get(name)).filter((tool)=>tool !== undefined);\n            const foundToolNames = filteredTools.map((t)=>t.function.name);\n            const missingTools = toolNames.filter((name)=>!foundToolNames.includes(name));\n            console.log('从数据库找到的工具:', foundToolNames);\n            if (missingTools.length > 0) {\n                console.warn('数据库中未找到的工具:', missingTools);\n            }\n            return filteredTools;\n        }\n        // 过滤用户请求的工具，并记录不存在的工具\n        console.log('开始过滤工具，请求的工具名称:', toolNames);\n        console.log('所有可用工具名称:', allTools.map((t)=>t.function.name));\n        const filteredTools = allTools.filter((tool)=>{\n            const isIncluded = toolNames.includes(tool.function.name);\n            console.log(`工具 ${tool.function.name} 是否匹配:`, isIncluded);\n            return isIncluded;\n        });\n        const foundToolNames = filteredTools.map((t)=>t.function.name);\n        const missingTools = toolNames.filter((name)=>!foundToolNames.includes(name));\n        if (missingTools.length > 0) {\n            console.warn('以下工具不存在:', missingTools);\n            console.warn('可能的原因: 工具名称不匹配或服务器未连接');\n        }\n        console.log('过滤后的工具:', filteredTools.map((t)=>t.function.name));\n        console.log('过滤后的工具数量:', filteredTools.length);\n        return filteredTools;\n    }\n    // 在客户端，使用缓存的availableTools\n    const filteredTools = availableTools.filter((tool)=>toolNames.includes(tool.function.name));\n    console.log('客户端过滤后的工具:', filteredTools.map((t)=>t.function.name));\n    return filteredTools;\n}\n/**\n * 初始化MCP工具系统\n */ async function initializeMcpTools() {\n    try {\n        const success = await _mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__.mcpClient.connect();\n        if (success) {\n            console.log('MCP工具系统初始化成功');\n            await refreshMcpTools();\n        } else {\n            console.warn('MCP工具系统初始化失败');\n        }\n        return success;\n    } catch (error) {\n        console.error('MCP工具系统初始化出错:', error);\n        return false;\n    }\n}\n/**\n * 刷新MCP工具列表\n */ async function refreshMcpTools() {\n    try {\n        if (_mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__.mcpClient.isClientConnected()) {\n            const mcpTools = await _mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__.mcpClient.refreshTools();\n            console.log(`已刷新 ${mcpTools.length} 个MCP工具`);\n        }\n    } catch (error) {\n        console.error('刷新MCP工具失败:', error);\n    }\n}\n/**\n * 获取所有可用工具（包括原有MCP工具和多服务器MCP工具）\n */ async function getAllAvailableTools() {\n    const tools = [];\n    // 0. 本地预设工具已在mcp-server.ts中定义，通过MCP协议获取，无需在此处重复定义\n    try {\n        // 1. 获取原有的MCP工具（仅在已连接时获取）\n        if (_mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__.mcpClient.isClientConnected()) {\n            const mcpTools = _mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__.mcpClient.getAvailableTools();\n            const convertedMcpTools = mcpTools.map((tool)=>convertMcpToolToOllamaFormat(tool)).filter((tool)=>validateOllamaTool(tool));\n            tools.push(...convertedMcpTools);\n        }\n        // 2. 获取多服务器MCP工具\n        try {\n            const response = await fetch('/api/mcp/tools');\n            const data = await response.json();\n            if (data.success && data.tools) {\n                const multiServerTools = data.tools.map((tool)=>{\n                    // 为多服务器工具添加服务器名称标识\n                    const toolWithServerInfo = {\n                        ...tool,\n                        description: tool.description + (tool.serverName ? ` (来自 ${tool.serverName})` : '')\n                    };\n                    return convertMcpToolToOllamaFormat(toolWithServerInfo);\n                }).filter((tool)=>validateOllamaTool(tool));\n                tools.push(...multiServerTools);\n            }\n        } catch (error) {\n            console.error('获取多服务器MCP工具失败:', error);\n        }\n    } catch (error) {\n        console.error('获取MCP工具失败:', error);\n    }\n    // 去重：根据工具名称去除重复项，保留最后一个（多服务器工具优先）\n    const uniqueTools = tools.reduce((acc, current)=>{\n        const existingIndex = acc.findIndex((tool)=>tool.function.name === current.function.name);\n        if (existingIndex >= 0) {\n            // 如果已存在同名工具，替换为当前工具（后加载的优先）\n            acc[existingIndex] = current;\n        } else {\n            acc.push(current);\n        }\n        return acc;\n    }, []);\n    console.log(`去重后获取到 ${uniqueTools.length} 个工具`);\n    return uniqueTools;\n}\n/**\n * 检查工具是否可用（检查MCP工具）\n */ async function isToolAvailable(toolName) {\n    try {\n        // 仅在已连接时检查工具可用性\n        if (_mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__.mcpClient.isClientConnected()) {\n            return _mcp_mcp_client__WEBPACK_IMPORTED_MODULE_0__.mcpClient.isToolAvailable(toolName);\n        }\n        // 如果未连接，检查多服务器客户端\n        if (true) {\n            // 服务器端\n            const { multiServerMcpClient } = __webpack_require__(/*! ./mcp/mcp-multi-server-client */ \"(rsc)/./src/lib/mcp/mcp-multi-server-client.ts\");\n            return multiServerMcpClient.isToolAvailable(toolName);\n        }\n        return false;\n    } catch (error) {\n        console.error('检查MCP工具可用性失败:', error);\n        return false;\n    }\n}\n// 工具执行函数\nclass ToolExecutor {\n    /**\n   * 执行工具调用（仅使用MCP工具）\n   */ static async executeToolCall(toolName, args, serverName) {\n        try {\n            // 只使用MCP工具\n            return await this.executeMcpTool(toolName, args, serverName);\n        } catch (error) {\n            return `工具执行失败: ${error instanceof Error ? error.message : '未知错误'}`;\n        }\n    }\n    /**\n   * 执行MCP工具调用\n   */ static async executeMcpTool(toolName, args, serverName) {\n        try {\n            // 在服务器端使用mcpServerClient和多服务器客户端，在客户端使用mcpClient\n            if (true) {\n                // 服务器端\n                const { mcpServerClient } = __webpack_require__(/*! ./mcp/mcp-client-server */ \"(rsc)/./src/lib/mcp/mcp-client-server.ts\");\n                const { multiServerMcpClient } = __webpack_require__(/*! ./mcp/mcp-multi-server-client */ \"(rsc)/./src/lib/mcp/mcp-multi-server-client.ts\");\n                // 首先尝试从原有MCP服务器调用工具\n                try {\n                    if (!mcpServerClient.isClientConnected()) {\n                        await mcpServerClient.connect();\n                    }\n                    if (mcpServerClient.isToolAvailable(toolName)) {\n                        const result = await mcpServerClient.callTool({\n                            name: toolName,\n                            arguments: args || {}\n                        });\n                        if (!result.isError && result.content) {\n                            const textContent = result.content.filter((item)=>item.type === 'text').map((item)=>item.text).join('\\n');\n                            return textContent || '工具执行成功';\n                        }\n                    }\n                } catch (error) {\n                    console.log('原有MCP服务器调用失败，尝试多服务器客户端:', error);\n                }\n                // 如果原有MCP服务器没有该工具或调用失败，尝试多服务器客户端\n                try {\n                    let targetServer = serverName;\n                    // 如果指定了服务器名称，优先使用指定的服务器\n                    if (serverName) {\n                        console.log(`使用指定的服务器: ${serverName}`);\n                        // 确保指定的服务器已连接\n                        const connectionStatus = multiServerMcpClient.getConnectionStatus();\n                        if (!connectionStatus[serverName]) {\n                            console.log(`连接指定的服务器: ${serverName}`);\n                            const connected = await multiServerMcpClient.connectServer(serverName);\n                            if (!connected) {\n                                throw new Error(`无法连接到指定的服务器 '${serverName}'`);\n                            }\n                        }\n                    } else {\n                        // 如果没有指定服务器，使用智能连接，只连接包含该工具的服务器\n                        console.log(`智能查找工具 '${toolName}' 所在的服务器`);\n                        targetServer = await multiServerMcpClient.connectForTool(toolName);\n                        if (!targetServer) {\n                            throw new Error(`找不到包含工具 '${toolName}' 的服务器`);\n                        }\n                    }\n                    const result = await multiServerMcpClient.callTool(toolName, args || {}, targetServer);\n                    if (!result.isError && result.content) {\n                        const textContent = result.content.filter((item)=>item.type === 'text').map((item)=>item.text).join('\\n');\n                        return textContent || '工具执行成功';\n                    } else {\n                        throw new Error(result.content?.[0]?.text || '工具调用失败');\n                    }\n                } catch (error) {\n                    console.error('多服务器MCP工具调用失败:', error);\n                    throw new Error('所有MCP服务器都无法执行该工具');\n                }\n            } else {}\n        } catch (error) {\n            console.error('MCP工具调用失败:', error);\n            throw error;\n        }\n    }\n} // 本地工具实现已删除，现在使用真正的MCP工具\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/tools.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ajv","vendor-chunks/zod","vendor-chunks/@modelcontextprotocol","vendor-chunks/uri-js","vendor-chunks/eventsource","vendor-chunks/cross-spawn","vendor-chunks/eventsource-parser","vendor-chunks/which","vendor-chunks/isexe","vendor-chunks/pkce-challenge","vendor-chunks/json-schema-traverse","vendor-chunks/fast-json-stable-stringify","vendor-chunks/fast-deep-equal","vendor-chunks/path-key","vendor-chunks/shebang-command","vendor-chunks/shebang-regex"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();