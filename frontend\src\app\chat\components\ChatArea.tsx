'use client';

import React from 'react';
import { ChatHeader } from '@/app/chat/components/ChatHeader';
import { MessageList } from '@/app/chat/components/MessageList';
import { ChatInput } from '@/app/chat/components/ChatInput';
import { ErrorDisplay } from '@/app/chat/components/ErrorDisplay';
import { Conversation, Message } from '@/lib/database';
import { AIState } from '../types';

interface ToolCallMessageType {
  id: string;
  type: 'tool_call';
  toolName: string;
  args: any;
  status: 'executing' | 'completed' | 'error';
  result?: string;
  error?: string;
  startTime: number;
  executionTime?: number;
}

interface ChatAreaProps {
  currentConversation: Conversation | null;
  messages: Message[];
  inputMessage: string;
  isStreaming: boolean;
  error: string | null;
  selectedModel: string;
  enableTools: boolean;
  selectedTools: string[];
  aiState?: AIState;
  activeToolCalls?: Map<string, any>;
  toolCallMessages: ToolCallMessageType[];
  onInputChange: (message: string) => void;
  onSendMessage: () => void;
  onStopGeneration: () => void;
  onKeyPress: (e: React.KeyboardEvent) => void;
  onToolsToggle: (enabled: boolean) => void;
  onSelectedToolsChange: (tools: string[]) => void;
  onClearChat?: () => void;
}

export function ChatArea({
  currentConversation,
  messages,
  inputMessage,
  isStreaming,
  error,
  selectedModel,
  enableTools,
  selectedTools,
  aiState,
  activeToolCalls,
  toolCallMessages,
  onInputChange,
  onSendMessage,
  onStopGeneration,
  onKeyPress,
  onToolsToggle,
  onSelectedToolsChange,
  onClearChat,
}: ChatAreaProps) {
  return (
    <div className="flex flex-col h-full">
      <ChatHeader currentConversation={currentConversation} />
      
      <div className="flex-1 min-h-0 overflow-hidden">
        <MessageList 
          messages={messages} 
          isStreaming={isStreaming} 
          aiState={aiState} 
          activeToolCalls={activeToolCalls}
          toolCallMessages={toolCallMessages}
        />
      </div>
      
      <div className="border-t border-gray-200 dark:border-gray-700 p-4 space-y-3">
        <ErrorDisplay error={error} />
        
        <ChatInput
          inputMessage={inputMessage}
          isStreaming={isStreaming}
          selectedModel={selectedModel}
          enableTools={enableTools}
          selectedTools={selectedTools}
          onInputChange={onInputChange}
          onSendMessage={onSendMessage}
          onStopGeneration={onStopGeneration}
          onKeyPress={onKeyPress}
          onToolsToggle={onToolsToggle}
          onSelectedToolsChange={onSelectedToolsChange}
          onClearChat={onClearChat}
        />
      </div>
    </div>
  );
}